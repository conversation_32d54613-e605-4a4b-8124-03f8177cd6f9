{"name": "cakephp/app", "description": "CakePHP skeleton app", "license": "MIT", "type": "project", "homepage": "https://cakephp.org", "require": {"php": ">=8.1", "cakephp/authentication": "^3.2", "cakephp/authorization": "^3.4", "cakephp/cakephp": "5.1.*", "cakephp/migrations": "^4.0.0", "cakephp/plugin-installer": "^2.0", "mobiledetect/mobiledetectlib": "^4.8.03"}, "require-dev": {"cakephp/bake": "^3.0.0", "cakephp/cakephp-codesniffer": "^5.0", "cakephp/debug_kit": "^5.0.0", "josegonzalez/dotenv": "^4.0", "phpunit/phpunit": "^10.5.5 || ^11.1.3"}, "suggest": {"cakephp/repl": "Console tools for a REPL interface for CakePHP applications.", "dereuromark/cakephp-ide-helper": "After baking your code, this keeps your annotations in sync with the code evolving from there on for maximum IDE and PHPStan/Psalm compatibility.", "markstory/asset_compress": "An asset compression plugin which provides file concatenation and a flexible filter system for preprocessing and minification.", "phpstan/phpstan": "PHPStan focuses on finding errors in your code without actually running it. It catches whole classes of bugs even before you write tests for the code."}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Test\\": "tests/", "Cake\\Test\\": "vendor/cakephp/cakephp/tests/"}}, "config": {"allow-plugins": {"cakephp/plugin-installer": true, "dealerdirect/phpcodesniffer-composer-installer": true}, "platform-check": true, "sort-packages": true}, "scripts": {"post-install-cmd": "App\\Console\\Installer::postInstall", "post-create-project-cmd": "App\\Console\\Installer::postInstall", "check": ["@test", "@cs-check"], "cs-check": "phpcs --colors -p", "cs-fix": "phpcbf --colors -p", "stan": "phpstan analyze", "test": "phpunit --colors=always"}}