<?php
/**
 * @var \App\View\AppView $this
 * @var \Cake\Datasource\EntityInterface $class
 */
?>
<div class="row">
    <aside class="column">
        <div class="side-nav">
            <h4 class="heading"><?= __('Actions') ?></h4>
            <?= $this->Html->link(__('Edit Class'), ['action' => 'edit', $class->id], ['class' => 'side-nav-item']) ?>
            <?= $this->Form->postLink(__('Delete Class'), ['action' => 'delete', $class->id], ['confirm' => __('Are you sure you want to delete # {0}?', $class->id), 'class' => 'side-nav-item']) ?>
            <?= $this->Html->link(__('List Classes'), ['action' => 'index'], ['class' => 'side-nav-item']) ?>
            <?= $this->Html->link(__('New Class'), ['action' => 'add'], ['class' => 'side-nav-item']) ?>
        </div>
    </aside>
    <div class="column column-80">
        <div class="classes view content">
            <h3><?= h($class->name) ?></h3>
            <table>
                <tr>
                    <th><?= __('Name') ?></th>
                    <td><?= h($class->name) ?></td>
                </tr>
                <tr>
                    <th><?= __('Teacher') ?></th>
                    <td><?= $class->hasValue('teacher') ? $this->Html->link($class->teacher->username, ['controller' => 'Users', 'action' => 'view', $class->teacher->id]) : '' ?></td>
                </tr>
                <tr>
                    <th><?= __('Status') ?></th>
                    <td><?= h($class->status) ?></td>
                </tr>
                <tr>
                    <th><?= __('Id') ?></th>
                    <td><?= $this->Number->format($class->id) ?></td>
                </tr>
                <tr>
                    <th><?= __('Max Students') ?></th>
                    <td><?= $this->Number->format($class->max_students) ?></td>
                </tr>
                <tr>
                    <th><?= __('Created') ?></th>
                    <td><?= h($class->created) ?></td>
                </tr>
                <tr>
                    <th><?= __('Modified') ?></th>
                    <td><?= h($class->modified) ?></td>
                </tr>
            </table>
            <div class="text">
                <strong><?= __('Description') ?></strong>
                <blockquote>
                    <?= $this->Text->autoParagraph(h($class->description)); ?>
                </blockquote>
            </div>
            <div class="related">
                <h4><?= __('Related Schedules') ?></h4>
                <?php if (!empty($class->schedules)) : ?>
                <div class="table-responsive">
                    <table>
                        <tr>
                            <th><?= __('Id') ?></th>
                            <th><?= __('Class Id') ?></th>
                            <th><?= __('Time Slot Id') ?></th>
                            <th><?= __('Teacher Id') ?></th>
                            <th><?= __('Schedule Date') ?></th>
                            <th><?= __('Status') ?></th>
                            <th><?= __('Notes') ?></th>
                            <th><?= __('Created') ?></th>
                            <th><?= __('Modified') ?></th>
                            <th class="actions"><?= __('Actions') ?></th>
                        </tr>
                        <?php foreach ($class->schedules as $schedule) : ?>
                        <tr>
                            <td><?= h($schedule->id) ?></td>
                            <td><?= h($schedule->class_id) ?></td>
                            <td><?= h($schedule->time_slot_id) ?></td>
                            <td><?= h($schedule->teacher_id) ?></td>
                            <td><?= h($schedule->schedule_date) ?></td>
                            <td><?= h($schedule->status) ?></td>
                            <td><?= h($schedule->notes) ?></td>
                            <td><?= h($schedule->created) ?></td>
                            <td><?= h($schedule->modified) ?></td>
                            <td class="actions">
                                <?= $this->Html->link(__('View'), ['controller' => 'Schedules', 'action' => 'view', $schedule->id]) ?>
                                <?= $this->Html->link(__('Edit'), ['controller' => 'Schedules', 'action' => 'edit', $schedule->id]) ?>
                                <?= $this->Form->postLink(
                                    __('Delete'),
                                    ['controller' => 'Schedules', 'action' => 'delete', $schedule->id],
                                    [
                                        'method' => 'delete',
                                        'confirm' => __('Are you sure you want to delete # {0}?', $schedule->id),
                                    ]
                                ) ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                <?php endif; ?>
            </div>
            <div class="related">
                <h4><?= __('Related Students') ?></h4>
                <?php if (!empty($class->students)) : ?>
                <div class="table-responsive">
                    <table>
                        <tr>
                            <th><?= __('Id') ?></th>
                            <th><?= __('Student Code') ?></th>
                            <th><?= __('Full Name') ?></th>
                            <th><?= __('Date Of Birth') ?></th>
                            <th><?= __('Parent Name') ?></th>
                            <th><?= __('Parent Phone') ?></th>
                            <th><?= __('Google Drive Link') ?></th>
                            <th><?= __('Class Id') ?></th>
                            <th><?= __('Status') ?></th>
                            <th><?= __('Created') ?></th>
                            <th><?= __('Modified') ?></th>
                            <th class="actions"><?= __('Actions') ?></th>
                        </tr>
                        <?php foreach ($class->students as $student) : ?>
                        <tr>
                            <td><?= h($student->id) ?></td>
                            <td><?= h($student->student_code) ?></td>
                            <td><?= h($student->full_name) ?></td>
                            <td><?= h($student->date_of_birth) ?></td>
                            <td><?= h($student->parent_name) ?></td>
                            <td><?= h($student->parent_phone) ?></td>
                            <td><?= h($student->google_drive_link) ?></td>
                            <td><?= h($student->class_id) ?></td>
                            <td><?= h($student->status) ?></td>
                            <td><?= h($student->created) ?></td>
                            <td><?= h($student->modified) ?></td>
                            <td class="actions">
                                <?= $this->Html->link(__('View'), ['controller' => 'Students', 'action' => 'view', $student->id]) ?>
                                <?= $this->Html->link(__('Edit'), ['controller' => 'Students', 'action' => 'edit', $student->id]) ?>
                                <?= $this->Form->postLink(
                                    __('Delete'),
                                    ['controller' => 'Students', 'action' => 'delete', $student->id],
                                    [
                                        'method' => 'delete',
                                        'confirm' => __('Are you sure you want to delete # {0}?', $student->id),
                                    ]
                                ) ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>