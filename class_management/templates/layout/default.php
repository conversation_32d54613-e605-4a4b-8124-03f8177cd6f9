<?php
/**
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @since         0.10.0
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 * @var \App\View\AppView $this
 */

$cakeDescription = 'Hệ thống quản lý lớp học';
?>
<!DOCTYPE html>
<html>
<head>
    <?= $this->Html->charset() ?>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>
        <?= $cakeDescription ?>:
        <?= $this->fetch('title') ?>
    </title>
    <?= $this->Html->meta('icon') ?>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <?= $this->fetch('meta') ?>
    <?= $this->fetch('css') ?>
    <?= $this->fetch('script') ?>
</head>
<body class="bg-gray-50 font-sans">
    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <?php if ($this->getRequest()->getSession()->check('Auth.User')): ?>
        <div class="hidden md:flex md:w-64 md:flex-col">
            <div class="flex flex-col flex-grow pt-5 bg-white border-r border-gray-200 overflow-y-auto">
                <div class="flex items-center flex-shrink-0 px-4">
                    <h1 class="text-xl font-bold text-gray-900">
                        <i class="fas fa-graduation-cap text-primary-600 mr-2"></i>
                        Quản lý lớp học
                    </h1>
                </div>
                <div class="mt-8 flex-grow flex flex-col">
                    <nav class="flex-1 px-2 space-y-1">
                        <!-- Dashboard -->
                        <a href="<?= $this->Url->build(['controller' => 'Dashboard', 'action' => 'index']) ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                            <i class="fas fa-tachometer-alt mr-3 text-gray-400 group-hover:text-gray-500"></i>
                            Dashboard
                        </a>

                        <!-- Quản lý nhân viên -->
                        <?php if (in_array($this->getRequest()->getSession()->read('Auth.User.role'), ['admin', 'manager'])): ?>
                        <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'index']) ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                            <i class="fas fa-users mr-3 text-gray-400 group-hover:text-gray-500"></i>
                            Quản lý nhân viên
                        </a>
                        <?php endif; ?>

                        <!-- Quản lý học sinh -->
                        <?php if (in_array($this->getRequest()->getSession()->read('Auth.User.role'), ['admin', 'manager'])): ?>
                        <a href="<?= $this->Url->build(['controller' => 'Students', 'action' => 'index']) ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                            <i class="fas fa-user-graduate mr-3 text-gray-400 group-hover:text-gray-500"></i>
                            Quản lý học sinh
                        </a>
                        <?php endif; ?>

                        <!-- Quản lý lớp học -->
                        <a href="<?= $this->Url->build(['controller' => 'Classes', 'action' => 'index']) ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                            <i class="fas fa-chalkboard mr-3 text-gray-400 group-hover:text-gray-500"></i>
                            Quản lý lớp học
                        </a>

                        <!-- Lịch dạy -->
                        <a href="<?= $this->Url->build(['controller' => 'Schedules', 'action' => 'index']) ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                            <i class="fas fa-calendar-alt mr-3 text-gray-400 group-hover:text-gray-500"></i>
                            Lịch dạy
                        </a>

                        <!-- Điểm danh -->
                        <a href="<?= $this->Url->build(['controller' => 'Attendance', 'action' => 'index']) ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                            <i class="fas fa-check-circle mr-3 text-gray-400 group-hover:text-gray-500"></i>
                            Điểm danh
                        </a>

                        <!-- Thu chi (Admin only) -->
                        <?php if ($this->getRequest()->getSession()->read('Auth.User.role') === 'admin'): ?>
                        <a href="<?= $this->Url->build(['controller' => 'Finances', 'action' => 'index']) ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                            <i class="fas fa-money-bill-wave mr-3 text-gray-400 group-hover:text-gray-500"></i>
                            Quản lý thu chi
                        </a>

                        <!-- Tài sản (Admin only) -->
                        <a href="<?= $this->Url->build(['controller' => 'Assets', 'action' => 'index']) ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                            <i class="fas fa-laptop mr-3 text-gray-400 group-hover:text-gray-500"></i>
                            Quản lý tài sản
                        </a>
                        <?php endif; ?>

                        <!-- Thông báo -->
                        <a href="<?= $this->Url->build(['controller' => 'Notifications', 'action' => 'index']) ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                            <i class="fas fa-bell mr-3 text-gray-400 group-hover:text-gray-500"></i>
                            Thông báo
                        </a>
                    </nav>
                </div>

                <!-- User info -->
                <div class="flex-shrink-0 flex border-t border-gray-200 p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-700">
                                <?= h($this->getRequest()->getSession()->read('Auth.User.full_name')) ?>
                            </p>
                            <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'logout']) ?>"
                               class="text-xs text-gray-500 hover:text-gray-700">
                                Đăng xuất
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Main content -->
        <div class="flex flex-col flex-1 overflow-hidden">
            <!-- Top bar for mobile -->
            <div class="md:hidden bg-white border-b border-gray-200 px-4 py-2">
                <div class="flex items-center justify-between">
                    <h1 class="text-lg font-semibold text-gray-900">Quản lý lớp học</h1>
                    <?php if ($this->getRequest()->getSession()->check('Auth.User')): ?>
                        <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'logout']) ?>"
                           class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-sign-out-alt"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 relative overflow-y-auto focus:outline-none">
                <div class="py-6">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
                        <!-- Flash messages -->
                        <?= $this->Flash->render() ?>

                        <!-- Page content -->
                        <?= $this->fetch('content') ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
