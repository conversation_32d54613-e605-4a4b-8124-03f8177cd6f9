<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Notification $notification
 * @var string[]|\Cake\Collection\CollectionInterface $targetUsers
 * @var string[]|\Cake\Collection\CollectionInterface $schedules
 */
?>
<div class="row">
    <aside class="column">
        <div class="side-nav">
            <h4 class="heading"><?= __('Actions') ?></h4>
            <?= $this->Form->postLink(
                __('Delete'),
                ['action' => 'delete', $notification->id],
                ['confirm' => __('Are you sure you want to delete # {0}?', $notification->id), 'class' => 'side-nav-item']
            ) ?>
            <?= $this->Html->link(__('List Notifications'), ['action' => 'index'], ['class' => 'side-nav-item']) ?>
        </div>
    </aside>
    <div class="column column-80">
        <div class="notifications form content">
            <?= $this->Form->create($notification) ?>
            <fieldset>
                <legend><?= __('Edit Notification') ?></legend>
                <?php
                    echo $this->Form->control('title');
                    echo $this->Form->control('message');
                    echo $this->Form->control('type');
                    echo $this->Form->control('target_audience');
                    echo $this->Form->control('target_user_id', ['options' => $targetUsers, 'empty' => true]);
                    echo $this->Form->control('schedule_id', ['options' => $schedules, 'empty' => true]);
                    echo $this->Form->control('is_read');
                    echo $this->Form->control('sent_by');
                    echo $this->Form->control('sent_at');
                ?>
            </fieldset>
            <?= $this->Form->button(__('Submit')) ?>
            <?= $this->Form->end() ?>
        </div>
    </div>
</div>
