<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Notification> $notifications
 */
?>
<div class="notifications index content">
    <?= $this->Html->link(__('New Notification'), ['action' => 'add'], ['class' => 'button float-right']) ?>
    <h3><?= __('Notifications') ?></h3>
    <div class="table-responsive">
        <table>
            <thead>
                <tr>
                    <th><?= $this->Paginator->sort('id') ?></th>
                    <th><?= $this->Paginator->sort('title') ?></th>
                    <th><?= $this->Paginator->sort('type') ?></th>
                    <th><?= $this->Paginator->sort('target_audience') ?></th>
                    <th><?= $this->Paginator->sort('target_user_id') ?></th>
                    <th><?= $this->Paginator->sort('schedule_id') ?></th>
                    <th><?= $this->Paginator->sort('is_read') ?></th>
                    <th><?= $this->Paginator->sort('sent_by') ?></th>
                    <th><?= $this->Paginator->sort('sent_at') ?></th>
                    <th><?= $this->Paginator->sort('created') ?></th>
                    <th><?= $this->Paginator->sort('modified') ?></th>
                    <th class="actions"><?= __('Actions') ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($notifications as $notification): ?>
                <tr>
                    <td><?= $this->Number->format($notification->id) ?></td>
                    <td><?= h($notification->title) ?></td>
                    <td><?= h($notification->type) ?></td>
                    <td><?= h($notification->target_audience) ?></td>
                    <td><?= $notification->hasValue('target_user') ? $this->Html->link($notification->target_user->username, ['controller' => 'Users', 'action' => 'view', $notification->target_user->id]) : '' ?></td>
                    <td><?= $notification->hasValue('schedule') ? $this->Html->link($notification->schedule->status, ['controller' => 'Schedules', 'action' => 'view', $notification->schedule->id]) : '' ?></td>
                    <td><?= h($notification->is_read) ?></td>
                    <td><?= $this->Number->format($notification->sent_by) ?></td>
                    <td><?= h($notification->sent_at) ?></td>
                    <td><?= h($notification->created) ?></td>
                    <td><?= h($notification->modified) ?></td>
                    <td class="actions">
                        <?= $this->Html->link(__('View'), ['action' => 'view', $notification->id]) ?>
                        <?= $this->Html->link(__('Edit'), ['action' => 'edit', $notification->id]) ?>
                        <?= $this->Form->postLink(
                            __('Delete'),
                            ['action' => 'delete', $notification->id],
                            [
                                'method' => 'delete',
                                'confirm' => __('Are you sure you want to delete # {0}?', $notification->id),
                            ]
                        ) ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <div class="paginator">
        <ul class="pagination">
            <?= $this->Paginator->first('<< ' . __('first')) ?>
            <?= $this->Paginator->prev('< ' . __('previous')) ?>
            <?= $this->Paginator->numbers() ?>
            <?= $this->Paginator->next(__('next') . ' >') ?>
            <?= $this->Paginator->last(__('last') . ' >>') ?>
        </ul>
        <p><?= $this->Paginator->counter(__('Page {{page}} of {{pages}}, showing {{current}} record(s) out of {{count}} total')) ?></p>
    </div>
</div>