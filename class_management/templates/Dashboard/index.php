<?php
/**
 * @var \App\View\AppView $this
 * @var array $stats
 * @var \App\Model\Entity\User $user
 * @var array $todaySchedules
 */
?>
<!-- Page header -->
<div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
    <p class="mt-2 text-gray-600">Xin chào, <?= h($user->full_name) ?> (<?= ucfirst($user->role) ?>)</p>
</div>

<!-- Stats grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <?php if ($user->isAdmin() || $user->isManager()): ?>
    <!-- Tổng số học sinh -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-user-graduate text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Tổng số học sinh</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['total_students'] ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Tổng số lớp học -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-chalkboard text-green-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Tổng số lớp học</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['total_classes'] ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Lịch dạy hôm nay -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-calendar-alt text-yellow-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Lịch dạy hôm nay</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['today_schedules'] ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <?php if ($user->isAdmin()): ?>
    <!-- Tổng nhân viên -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-users text-purple-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Tổng nhân viên</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['total_users'] ?? 0 ?></p>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <?php else: ?>
    <!-- Teacher view - simplified stats -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-chalkboard text-green-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Lớp của tôi</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['my_classes'] ?? 0 ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-calendar-alt text-yellow-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Lịch dạy hôm nay</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['my_schedules_today'] ?? 0 ?></p>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Today's Schedule -->
<div class="bg-white shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">
            <i class="fas fa-calendar-day mr-2 text-primary-600"></i>
            Lịch dạy hôm nay (<?= date('d/m/Y') ?>)
        </h2>
    </div>
    <div class="p-6">
        <?php if (!empty($todaySchedules)): ?>
            <div class="space-y-4">
                <?php foreach ($todaySchedules as $schedule): ?>
                <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-clock text-primary-600"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-gray-900"><?= h($schedule['class_name']) ?></h3>
                                <p class="text-sm text-gray-500">
                                    <i class="fas fa-user mr-1"></i>
                                    <?= h($schedule['teacher_name']) ?>
                                </p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900">
                                    <?= h($schedule['start_time']) ?> - <?= h($schedule['end_time']) ?>
                                </p>
                                <p class="text-sm text-gray-500"><?= h($schedule['time_slot_name']) ?></p>
                            </div>
                        </div>
                        <?php if (!empty($schedule['notes'])): ?>
                        <p class="mt-2 text-sm text-gray-600">
                            <i class="fas fa-sticky-note mr-1"></i>
                            <?= h($schedule['notes']) ?>
                        </p>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-8">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-calendar-times text-gray-400 text-xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Không có lịch dạy</h3>
                <p class="text-gray-500">Hôm nay bạn không có lịch dạy nào được lên lịch.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Actions -->
<div class="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    <?php if ($user->isAdmin() || $user->isManager()): ?>
    <a href="<?= $this->Url->build(['controller' => 'Students', 'action' => 'add']) ?>" 
       class="flex items-center p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
        <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                <i class="fas fa-user-plus text-blue-600"></i>
            </div>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">Thêm học sinh</p>
            <p class="text-xs text-gray-500">Đăng ký học sinh mới</p>
        </div>
    </a>
    
    <a href="<?= $this->Url->build(['controller' => 'Classes', 'action' => 'add']) ?>" 
       class="flex items-center p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
        <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                <i class="fas fa-plus text-green-600"></i>
            </div>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">Tạo lớp học</p>
            <p class="text-xs text-gray-500">Thêm lớp học mới</p>
        </div>
    </a>
    <?php endif; ?>
    
    <a href="<?= $this->Url->build(['controller' => 'Schedules', 'action' => 'index']) ?>" 
       class="flex items-center p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
        <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                <i class="fas fa-calendar text-yellow-600"></i>
            </div>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">Xem lịch dạy</p>
            <p class="text-xs text-gray-500">Quản lý lịch học</p>
        </div>
    </a>
    
    <a href="<?= $this->Url->build(['controller' => 'Attendance', 'action' => 'index']) ?>" 
       class="flex items-center p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
        <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                <i class="fas fa-check-circle text-purple-600"></i>
            </div>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">Điểm danh</p>
            <p class="text-xs text-gray-500">Ghi nhận điểm danh</p>
        </div>
    </a>
</div>
