<?php
/**
 * @var \App\View\AppView $this
 * @var array $stats
 * @var \App\Model\Entity\User $user
 * @var array $todaySchedules
 */
?>
<!-- Page header -->
<div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
    <p class="mt-2 text-gray-600">Xin chào, <?= h($user->full_name) ?> (<?= ucfirst($user->role) ?>)</p>
</div>

<!-- Stats grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <?php if ($user->isAdmin() || $user->isManager()): ?>
    <!-- Tổng số học sinh -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-user-graduate text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Tổng số học sinh</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['total_students'] ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tổng số lớp học -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-chalkboard text-green-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Tổng số lớp học</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['total_classes'] ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Lịch dạy hôm nay -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-calendar-alt text-yellow-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Lịch dạy hôm nay</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['today_schedules'] ?></p>
                </div>
            </div>
        </div>
    </div>
            <div class="stat-card">
                <h3>Học sinh vắng hôm nay</h3>
                <div class="stat-number"><?= $stats['absent_today'] ?></div>
            </div>
        <?php else: ?>
            <div class="stat-card">
                <h3>Lớp của tôi</h3>
                <div class="stat-number"><?= $stats['my_classes'] ?></div>
            </div>
            <div class="stat-card">
                <h3>Lịch dạy hôm nay</h3>
                <div class="stat-number"><?= $stats['my_schedules_today'] ?></div>
            </div>
        <?php endif; ?>
    </div>

    <div class="today-schedule">
        <h2>Lịch dạy hôm nay (<?= date('d/m/Y') ?>)</h2>
        <?php if (!empty($todaySchedules)): ?>
            <div class="schedule-list">
                <?php foreach ($todaySchedules as $schedule): ?>
                    <div class="schedule-item">
                        <div class="time">
                            <?= h($schedule->time_slot->start_time->format('H:i')) ?> - 
                            <?= h($schedule->time_slot->end_time->format('H:i')) ?>
                        </div>
                        <div class="class-info">
                            <strong><?= h($schedule->class->name) ?></strong>
                            <?php if ($user->isAdmin() || $user->isManager()): ?>
                                <br><small>Giáo viên: <?= h($schedule->teacher->full_name) ?></small>
                            <?php endif; ?>
                        </div>
                        <div class="status">
                            <span class="status-<?= $schedule->status ?>"><?= ucfirst($schedule->status) ?></span>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <p>Không có lịch dạy nào hôm nay.</p>
        <?php endif; ?>
    </div>

    <div class="quick-actions">
        <h2>Thao tác nhanh</h2>
        <div class="action-buttons">
            <?php if ($user->isAdmin()): ?>
                <?= $this->Html->link('Quản lý nhân viên', ['controller' => 'Users', 'action' => 'index'], ['class' => 'btn btn-primary']) ?>
                <?= $this->Html->link('Quản lý học sinh', ['controller' => 'Students', 'action' => 'index'], ['class' => 'btn btn-primary']) ?>
                <?= $this->Html->link('Quản lý thu chi', ['controller' => 'Finances', 'action' => 'index'], ['class' => 'btn btn-primary']) ?>
                <?= $this->Html->link('Quản lý tài sản', ['controller' => 'Assets', 'action' => 'index'], ['class' => 'btn btn-primary']) ?>
            <?php endif; ?>
            
            <?php if ($user->isAdmin() || $user->isManager()): ?>
                <?= $this->Html->link('Quản lý lớp học', ['controller' => 'Classes', 'action' => 'index'], ['class' => 'btn btn-secondary']) ?>
                <?= $this->Html->link('Quản lý lịch dạy', ['controller' => 'Schedules', 'action' => 'index'], ['class' => 'btn btn-secondary']) ?>
                <?= $this->Html->link('Điểm danh', ['controller' => 'Attendance', 'action' => 'index'], ['class' => 'btn btn-secondary']) ?>
                <?= $this->Html->link('Gửi thông báo', ['controller' => 'Notifications', 'action' => 'add'], ['class' => 'btn btn-secondary']) ?>
            <?php endif; ?>
            
            <?php if ($user->isTeacher()): ?>
                <?= $this->Html->link('Lớp của tôi', ['controller' => 'Classes', 'action' => 'myClasses'], ['class' => 'btn btn-secondary']) ?>
                <?= $this->Html->link('Check-in học sinh', ['controller' => 'Attendance', 'action' => 'checkin'], ['class' => 'btn btn-secondary']) ?>
                <?= $this->Html->link('Hồ sơ cá nhân', ['controller' => 'Users', 'action' => 'profile'], ['class' => 'btn btn-secondary']) ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.dashboard {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #eee;
}

.header h1 {
    color: #333;
    margin: 0;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logout-btn {
    background-color: #dc3545;
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
}

.logout-btn:hover {
    background-color: #c82333;
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-card h3 {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 16px;
}

.stat-number {
    font-size: 36px;
    font-weight: bold;
    color: #007bff;
}

.today-schedule {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.today-schedule h2 {
    margin-top: 0;
    color: #333;
}

.schedule-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.schedule-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #007bff;
}

.schedule-item .time {
    min-width: 120px;
    font-weight: bold;
    color: #007bff;
}

.schedule-item .class-info {
    flex: 1;
    margin-left: 20px;
}

.schedule-item .status {
    min-width: 100px;
    text-align: right;
}

.status-scheduled {
    background: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.quick-actions {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.quick-actions h2 {
    margin-top: 0;
    color: #333;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.btn {
    display: inline-block;
    padding: 12px 20px;
    text-decoration: none;
    border-radius: 6px;
    text-align: center;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
    color: white;
}
</style>
