<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\User $user
 */
?>
<div class="row">
    <aside class="column">
        <div class="side-nav">
            <h4 class="heading"><?= __('Thao tác') ?></h4>
            <?= $this->Html->link(__('Danh sách nhân viên'), ['action' => 'index'], ['class' => 'side-nav-item']) ?>
        </div>
    </aside>
    <div class="column column-80">
        <div class="users form content">
            <?= $this->Form->create($user) ?>
            <fieldset>
                <legend><?= __('Thêm nhân viên mới') ?></legend>
                <?= $this->Form->control('username', [
                    'label' => 'Tên đăng nhập',
                    'required' => true
                ]) ?>
                <?= $this->Form->control('email', [
                    'label' => 'Email',
                    'type' => 'email',
                    'required' => true
                ]) ?>
                <?= $this->Form->control('password', [
                    'label' => 'Mật khẩu',
                    'type' => 'password',
                    'required' => true
                ]) ?>
                <?= $this->Form->control('full_name', [
                    'label' => 'Họ và tên',
                    'required' => true
                ]) ?>
                <?= $this->Form->control('phone', [
                    'label' => 'Số điện thoại'
                ]) ?>
                <?= $this->Form->control('role', [
                    'label' => 'Vai trò',
                    'type' => 'select',
                    'options' => [
                        'admin' => 'Quản trị viên',
                        'manager' => 'Quản sinh',
                        'teacher' => 'Giáo viên'
                    ],
                    'empty' => 'Chọn vai trò',
                    'required' => true
                ]) ?>
                <?= $this->Form->control('status', [
                    'label' => 'Trạng thái',
                    'type' => 'select',
                    'options' => [
                        'active' => 'Hoạt động',
                        'inactive' => 'Không hoạt động'
                    ],
                    'default' => 'active'
                ]) ?>
            </fieldset>
            <?= $this->Form->button(__('Lưu'), ['class' => 'btn-primary']) ?>
            <?= $this->Form->end() ?>
        </div>
    </div>
</div>

<style>
.row {
    display: flex;
    margin: 20px;
}

.column {
    padding: 0 15px;
}

.column-80 {
    flex: 0 0 80%;
}

aside.column {
    flex: 0 0 20%;
}

.side-nav {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 4px;
}

.side-nav .heading {
    margin-top: 0;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
}

.side-nav-item {
    display: block;
    padding: 10px 0;
    color: #007bff;
    text-decoration: none;
}

.side-nav-item:hover {
    color: #0056b3;
    text-decoration: underline;
}

.users.form.content {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.users.form fieldset {
    border: none;
    padding: 0;
    margin: 0;
}

.users.form legend {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.users.form .input {
    margin-bottom: 20px;
}

.users.form label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.users.form input[type="text"],
.users.form input[type="email"],
.users.form input[type="password"],
.users.form select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 16px;
    box-sizing: border-box;
}

.users.form input:focus,
.users.form select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.users.form button {
    background-color: #007bff;
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    margin-top: 20px;
}

.users.form button:hover {
    background-color: #0056b3;
}

.users.form .btn-primary {
    background-color: #007bff;
}

.users.form .btn-primary:hover {
    background-color: #0056b3;
}

.error-message {
    color: #dc3545;
    font-size: 14px;
    margin-top: 5px;
}

.required {
    color: #dc3545;
}
</style>
