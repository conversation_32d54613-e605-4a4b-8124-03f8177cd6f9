<?php
/**
 * @var \App\View\AppView $this
 */
?>
<div class="users form">
    <?= $this->Flash->render() ?>
    <h3><PERSON><PERSON><PERSON> nhập hệ thống quản lý lớp học</h3>
    <?= $this->Form->create() ?>
    <fieldset>
        <legend><?= __('Vui lòng nhập thông tin đăng nhập') ?></legend>
        <?= $this->Form->control('email', [
            'label' => 'Email',
            'required' => true,
            'type' => 'email'
        ]) ?>
        <?= $this->Form->control('password', [
            'label' => 'Mật khẩu',
            'required' => true
        ]) ?>
    </fieldset>
    <?= $this->Form->submit(__('Đăng nhập')); ?>
    <?= $this->Form->end() ?>
    
    <div class="login-info">
        <h4>Thông tin đăng nhập mẫu:</h4>
        <p><strong>Admin:</strong> <EMAIL> / password</p>
        <p><strong><PERSON><PERSON><PERSON><PERSON> sinh:</strong> <EMAIL> / password</p>
        <p><strong>G<PERSON><PERSON><PERSON> viên:</strong> <EMAIL> / password</p>
    </div>
</div>

<style>
.users.form {
    max-width: 400px;
    margin: 50px auto;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.users.form h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
}

.users.form fieldset {
    border: none;
    padding: 0;
}

.users.form legend {
    font-weight: bold;
    margin-bottom: 15px;
}

.users.form .input {
    margin-bottom: 15px;
}

.users.form label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.users.form input[type="email"],
.users.form input[type="password"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 3px;
    box-sizing: border-box;
}

.users.form input[type="submit"] {
    width: 100%;
    padding: 10px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 16px;
}

.users.form input[type="submit"]:hover {
    background-color: #0056b3;
}

.login-info {
    margin-top: 20px;
    padding: 15px;
    background-color: #e9ecef;
    border-radius: 3px;
}

.login-info h4 {
    margin-top: 0;
    color: #495057;
}

.login-info p {
    margin: 5px 0;
    font-size: 14px;
}
</style>
