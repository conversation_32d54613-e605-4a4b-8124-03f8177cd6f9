<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\User> $users
 */
?>
<div class="users index content">
    <?= $this->Html->link(__('Thêm nhân viên mới'), ['action' => 'add'], ['class' => 'button float-right']) ?>
    <h3><?= __('Quản lý nhân viên') ?></h3>
    <div class="table-responsive">
        <table>
            <thead>
                <tr>
                    <th><?= $this->Paginator->sort('id') ?></th>
                    <th><?= $this->Paginator->sort('username', 'Tên đăng nhập') ?></th>
                    <th><?= $this->Paginator->sort('email', 'Email') ?></th>
                    <th><?= $this->Paginator->sort('full_name', 'Họ tên') ?></th>
                    <th><?= $this->Paginator->sort('phone', 'Số điện thoại') ?></th>
                    <th><?= $this->Paginator->sort('role', 'Vai trò') ?></th>
                    <th><?= $this->Paginator->sort('status', 'Trạng thái') ?></th>
                    <th><?= $this->Paginator->sort('created', 'Ngày tạo') ?></th>
                    <th class="actions"><?= __('Thao tác') ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($users as $user): ?>
                <tr>
                    <td><?= $this->Number->format($user->id) ?></td>
                    <td><?= h($user->username) ?></td>
                    <td><?= h($user->email) ?></td>
                    <td><?= h($user->full_name) ?></td>
                    <td><?= h($user->phone) ?></td>
                    <td>
                        <span class="role-badge role-<?= $user->role ?>">
                            <?php
                            $roleNames = [
                                'admin' => 'Quản trị viên',
                                'manager' => 'Quản sinh',
                                'teacher' => 'Giáo viên'
                            ];
                            echo $roleNames[$user->role] ?? $user->role;
                            ?>
                        </span>
                    </td>
                    <td>
                        <span class="status-badge status-<?= $user->status ?>">
                            <?= $user->status === 'active' ? 'Hoạt động' : 'Không hoạt động' ?>
                        </span>
                    </td>
                    <td><?= h($user->created->format('d/m/Y H:i')) ?></td>
                    <td class="actions">
                        <?= $this->Html->link(__('Xem'), ['action' => 'view', $user->id]) ?>
                        <?= $this->Html->link(__('Sửa'), ['action' => 'edit', $user->id]) ?>
                        <?= $this->Form->postLink(__('Xóa'), ['action' => 'delete', $user->id], ['confirm' => __('Bạn có chắc muốn xóa # {0}?', $user->id)]) ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <div class="paginator">
        <ul class="pagination">
            <?= $this->Paginator->first('<< ' . __('đầu')) ?>
            <?= $this->Paginator->prev('< ' . __('trước')) ?>
            <?= $this->Paginator->numbers() ?>
            <?= $this->Paginator->next(__('sau') . ' >') ?>
            <?= $this->Paginator->last(__('cuối') . ' >>') ?>
        </ul>
        <p><?= $this->Paginator->counter(__('Trang {{page}} / {{pages}}, hiển thị {{current}} bản ghi / {{count}} tổng cộng')) ?></p>
    </div>
</div>

<style>
.users.index.content {
    padding: 20px;
}

.button {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 4px;
    display: inline-block;
    margin-bottom: 20px;
}

.button:hover {
    background-color: #0056b3;
    color: white;
}

.float-right {
    float: right;
}

.table-responsive {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

table th,
table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

table tr:hover {
    background-color: #f5f5f5;
}

.role-badge,
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.role-admin {
    background-color: #dc3545;
    color: white;
}

.role-manager {
    background-color: #ffc107;
    color: black;
}

.role-teacher {
    background-color: #28a745;
    color: white;
}

.status-active {
    background-color: #28a745;
    color: white;
}

.status-inactive {
    background-color: #6c757d;
    color: white;
}

.actions a {
    margin-right: 10px;
    color: #007bff;
    text-decoration: none;
}

.actions a:hover {
    text-decoration: underline;
}

.pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.pagination li {
    margin-right: 5px;
}

.pagination a {
    display: block;
    padding: 8px 12px;
    color: #007bff;
    text-decoration: none;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.pagination a:hover {
    background-color: #e9ecef;
}

.pagination .active a {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}
</style>
