<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\User $user
 */
?>
<div class="row">
    <aside class="column">
        <div class="side-nav">
            <h4 class="heading"><?= __('Thao tác') ?></h4>
            <?= $this->Html->link(__('Sửa nhân viên'), ['action' => 'edit', $user->id], ['class' => 'side-nav-item']) ?>
            <?= $this->Form->postLink(__('Xóa nhân viên'), ['action' => 'delete', $user->id], ['confirm' => __('Bạn có chắc muốn xóa {0}?', $user->full_name), 'class' => 'side-nav-item']) ?>
            <?= $this->Html->link(__('Danh sách nhân viên'), ['action' => 'index'], ['class' => 'side-nav-item']) ?>
            <?= $this->Html->link(__('Thêm nhân viên mới'), ['action' => 'add'], ['class' => 'side-nav-item']) ?>
        </div>
    </aside>
    <div class="column column-80">
        <div class="users view content">
            <h3><?= h($user->full_name) ?></h3>
            <table>
                <tr>
                    <th><?= __('ID') ?></th>
                    <td><?= $this->Number->format($user->id) ?></td>
                </tr>
                <tr>
                    <th><?= __('Tên đăng nhập') ?></th>
                    <td><?= h($user->username) ?></td>
                </tr>
                <tr>
                    <th><?= __('Email') ?></th>
                    <td><?= h($user->email) ?></td>
                </tr>
                <tr>
                    <th><?= __('Họ và tên') ?></th>
                    <td><?= h($user->full_name) ?></td>
                </tr>
                <tr>
                    <th><?= __('Số điện thoại') ?></th>
                    <td><?= h($user->phone) ?></td>
                </tr>
                <tr>
                    <th><?= __('Vai trò') ?></th>
                    <td>
                        <span class="role-badge role-<?= $user->role ?>">
                            <?php
                            $roleNames = [
                                'admin' => 'Quản trị viên',
                                'manager' => 'Quản sinh',
                                'teacher' => 'Giáo viên'
                            ];
                            echo $roleNames[$user->role] ?? $user->role;
                            ?>
                        </span>
                    </td>
                </tr>
                <tr>
                    <th><?= __('Trạng thái') ?></th>
                    <td>
                        <span class="status-badge status-<?= $user->status ?>">
                            <?= $user->status === 'active' ? 'Hoạt động' : 'Không hoạt động' ?>
                        </span>
                    </td>
                </tr>
                <tr>
                    <th><?= __('Ngày tạo') ?></th>
                    <td><?= h($user->created->format('d/m/Y H:i:s')) ?></td>
                </tr>
                <tr>
                    <th><?= __('Ngày cập nhật') ?></th>
                    <td><?= h($user->modified->format('d/m/Y H:i:s')) ?></td>
                </tr>
            </table>
        </div>
    </div>
</div>

<style>
.row {
    display: flex;
    margin: 20px;
}

.column {
    padding: 0 15px;
}

.column-80 {
    flex: 0 0 80%;
}

aside.column {
    flex: 0 0 20%;
}

.side-nav {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 4px;
}

.side-nav .heading {
    margin-top: 0;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
}

.side-nav-item {
    display: block;
    padding: 10px 0;
    color: #007bff;
    text-decoration: none;
    border-bottom: 1px solid #eee;
}

.side-nav-item:hover {
    color: #0056b3;
    text-decoration: underline;
}

.users.view.content {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.users.view.content h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-bottom: 30px;
}

.users.view.content table {
    width: 100%;
    border-collapse: collapse;
}

.users.view.content table th,
.users.view.content table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.users.view.content table th {
    background-color: #f8f9fa;
    font-weight: bold;
    width: 200px;
    color: #495057;
}

.users.view.content table tr:hover {
    background-color: #f5f5f5;
}

.role-badge,
.status-badge {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.role-admin {
    background-color: #dc3545;
    color: white;
}

.role-manager {
    background-color: #ffc107;
    color: black;
}

.role-teacher {
    background-color: #28a745;
    color: white;
}

.status-active {
    background-color: #28a745;
    color: white;
}

.status-inactive {
    background-color: #6c757d;
    color: white;
}
</style>
