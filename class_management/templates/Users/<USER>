<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\User> $users
 */
?>
<!-- Page header -->
<div class="sm:flex sm:items-center sm:justify-between mb-8">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Quản lý nhân viên</h1>
        <p class="mt-2 text-gray-600">Danh sách tất cả nhân viên trong hệ thống</p>
    </div>
    <div class="mt-4 sm:mt-0">
        <?= $this->Html->link(
            '<i class="fas fa-plus mr-2"></i>Thêm nhân viên mới',
            ['action' => 'add'],
            [
                'class' => 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500',
                'escape' => false
            ]
        ) ?>
    </div>
</div>

<!-- Users table -->
<div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <?= $this->Paginator->sort('id', 'ID') ?>
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <?= $this->Paginator->sort('full_name', 'Họ và tên') ?>
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <?= $this->Paginator->sort('email', 'Email') ?>
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <?= $this->Paginator->sort('role', 'Vai trò') ?>
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <?= $this->Paginator->sort('status', 'Trạng thái') ?>
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <?= $this->Paginator->sort('created', 'Ngày tạo') ?>
                    </th>
                    <th scope="col" class="relative px-6 py-3">
                        <span class="sr-only">Thao tác</span>
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php foreach ($users as $user): ?>
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        #<?= $this->Number->format($user->id) ?>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-8 w-8">
                                <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                    <i class="fas fa-user text-gray-600 text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900"><?= h($user->full_name) ?></div>
                                <div class="text-sm text-gray-500"><?= h($user->username) ?></div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <?= h($user->email) ?>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <?php
                        $roleColors = [
                            'admin' => 'bg-red-100 text-red-800',
                            'manager' => 'bg-yellow-100 text-yellow-800',
                            'teacher' => 'bg-green-100 text-green-800'
                        ];
                        $roleNames = [
                            'admin' => 'Quản trị viên',
                            'manager' => 'Quản sinh',
                            'teacher' => 'Giáo viên'
                        ];
                        ?>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?= $roleColors[$user->role] ?? 'bg-gray-100 text-gray-800' ?>">
                            <?= $roleNames[$user->role] ?? $user->role ?>
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <?php if ($user->status === 'active'): ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                <i class="fas fa-check-circle mr-1"></i>
                                Hoạt động
                            </span>
                        <?php else: ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                <i class="fas fa-times-circle mr-1"></i>
                                Không hoạt động
                            </span>
                        <?php endif; ?>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <?= h($user->created->format('d/m/Y')) ?>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center space-x-2">
                            <?= $this->Html->link(
                                '<i class="fas fa-eye"></i>',
                                ['action' => 'view', $user->id],
                                [
                                    'class' => 'text-primary-600 hover:text-primary-900 p-1',
                                    'escape' => false,
                                    'title' => 'Xem chi tiết'
                                ]
                            ) ?>
                            <?= $this->Html->link(
                                '<i class="fas fa-edit"></i>',
                                ['action' => 'edit', $user->id],
                                [
                                    'class' => 'text-yellow-600 hover:text-yellow-900 p-1',
                                    'escape' => false,
                                    'title' => 'Chỉnh sửa'
                                ]
                            ) ?>
                            <?= $this->Form->postLink(
                                '<i class="fas fa-trash"></i>',
                                ['action' => 'delete', $user->id],
                                [
                                    'confirm' => __('Bạn có chắc muốn xóa {0}?', $user->full_name),
                                    'class' => 'text-red-600 hover:text-red-900 p-1',
                                    'escape' => false,
                                    'title' => 'Xóa'
                                ]
                            ) ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <div class="paginator">
        <ul class="pagination">
            <?= $this->Paginator->first('<< ' . __('đầu')) ?>
            <?= $this->Paginator->prev('< ' . __('trước')) ?>
            <?= $this->Paginator->numbers() ?>
            <?= $this->Paginator->next(__('sau') . ' >') ?>
            <?= $this->Paginator->last(__('cuối') . ' >>') ?>
        </ul>
        <p><?= $this->Paginator->counter(__('Trang {{page}} / {{pages}}, hiển thị {{current}} bản ghi / {{count}} tổng cộng')) ?></p>
    </div>
</div>

<style>
.users.index.content {
    padding: 20px;
}

.button {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 4px;
    display: inline-block;
    margin-bottom: 20px;
}

.button:hover {
    background-color: #0056b3;
    color: white;
}

.float-right {
    float: right;
}

.table-responsive {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

table th,
table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

table tr:hover {
    background-color: #f5f5f5;
}

.role-badge,
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.role-admin {
    background-color: #dc3545;
    color: white;
}

.role-manager {
    background-color: #ffc107;
    color: black;
}

.role-teacher {
    background-color: #28a745;
    color: white;
}

.status-active {
    background-color: #28a745;
    color: white;
}

.status-inactive {
    background-color: #6c757d;
    color: white;
}

.actions a {
    margin-right: 10px;
    color: #007bff;
    text-decoration: none;
}

.actions a:hover {
    text-decoration: underline;
}

.pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.pagination li {
    margin-right: 5px;
}

.pagination a {
    display: block;
    padding: 8px 12px;
    color: #007bff;
    text-decoration: none;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.pagination a:hover {
    background-color: #e9ecef;
}

.pagination .active a {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}
</style>
