<?php
/**
 * @var \App\View\AppView $this
 */
?>
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100">
                <i class="fas fa-graduation-cap text-primary-600 text-xl"></i>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                <PERSON><PERSON><PERSON> nhập hệ thống
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                H<PERSON> thống quản lý lớp học
            </p>
        </div>
        
        <?= $this->Flash->render() ?>
        
        <div class="bg-white py-8 px-6 shadow-lg rounded-lg border border-gray-200">
            <?= $this->Form->create(null, [
                'class' => 'space-y-6'
            ]) ?>
            
            <div>
                <?= $this->Form->control('email', [
                    'type' => 'email',
                    'required' => true,
                    'label' => [
                        'text' => 'Email',
                        'class' => 'block text-sm font-medium text-gray-700 mb-2'
                    ],
                    'class' => 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm',
                    'placeholder' => 'Nhập email của bạn'
                ]) ?>
            </div>
            
            <div>
                <?= $this->Form->control('password', [
                    'type' => 'password',
                    'required' => true,
                    'label' => [
                        'text' => 'Mật khẩu',
                        'class' => 'block text-sm font-medium text-gray-700 mb-2'
                    ],
                    'class' => 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm',
                    'placeholder' => 'Nhập mật khẩu'
                ]) ?>
            </div>
            
            <div>
                <?= $this->Form->submit('Đăng nhập', [
                    'class' => 'group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out'
                ]) ?>
            </div>
            
            <?= $this->Form->end() ?>
        </div>
        
        <!-- Demo accounts info -->
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">
                        Tài khoản demo
                    </h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li><strong>Admin:</strong> <EMAIL> / password</li>
                            <li><strong>Quản sinh:</strong> <EMAIL> / password</li>
                            <li><strong>Giáo viên:</strong> <EMAIL> / password</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
