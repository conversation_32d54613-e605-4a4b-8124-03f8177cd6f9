<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Student $student
 */
?>
<div class="row">
    <aside class="column">
        <div class="side-nav">
            <h4 class="heading"><?= __('Actions') ?></h4>
            <?= $this->Html->link(__('Edit Student'), ['action' => 'edit', $student->id], ['class' => 'side-nav-item']) ?>
            <?= $this->Form->postLink(__('Delete Student'), ['action' => 'delete', $student->id], ['confirm' => __('Are you sure you want to delete # {0}?', $student->id), 'class' => 'side-nav-item']) ?>
            <?= $this->Html->link(__('List Students'), ['action' => 'index'], ['class' => 'side-nav-item']) ?>
            <?= $this->Html->link(__('New Student'), ['action' => 'add'], ['class' => 'side-nav-item']) ?>
        </div>
    </aside>
    <div class="column column-80">
        <div class="students view content">
            <h3><?= h($student->student_code) ?></h3>
            <table>
                <tr>
                    <th><?= __('Student Code') ?></th>
                    <td><?= h($student->student_code) ?></td>
                </tr>
                <tr>
                    <th><?= __('Full Name') ?></th>
                    <td><?= h($student->full_name) ?></td>
                </tr>
                <tr>
                    <th><?= __('Parent Name') ?></th>
                    <td><?= h($student->parent_name) ?></td>
                </tr>
                <tr>
                    <th><?= __('Parent Phone') ?></th>
                    <td><?= h($student->parent_phone) ?></td>
                </tr>
                <tr>
                    <th><?= __('Class') ?></th>
                    <td><?= $student->hasValue('class') ? $this->Html->link($student->class->name, ['controller' => 'Classes', 'action' => 'view', $student->class->id]) : '' ?></td>
                </tr>
                <tr>
                    <th><?= __('Status') ?></th>
                    <td><?= h($student->status) ?></td>
                </tr>
                <tr>
                    <th><?= __('Id') ?></th>
                    <td><?= $this->Number->format($student->id) ?></td>
                </tr>
                <tr>
                    <th><?= __('Date Of Birth') ?></th>
                    <td><?= h($student->date_of_birth) ?></td>
                </tr>
                <tr>
                    <th><?= __('Created') ?></th>
                    <td><?= h($student->created) ?></td>
                </tr>
                <tr>
                    <th><?= __('Modified') ?></th>
                    <td><?= h($student->modified) ?></td>
                </tr>
            </table>
            <div class="text">
                <strong><?= __('Google Drive Link') ?></strong>
                <blockquote>
                    <?= $this->Text->autoParagraph(h($student->google_drive_link)); ?>
                </blockquote>
            </div>
            <div class="related">
                <h4><?= __('Related Attendance') ?></h4>
                <?php if (!empty($student->attendance)) : ?>
                <div class="table-responsive">
                    <table>
                        <tr>
                            <th><?= __('Id') ?></th>
                            <th><?= __('Schedule Id') ?></th>
                            <th><?= __('Student Id') ?></th>
                            <th><?= __('Status') ?></th>
                            <th><?= __('Check In Time') ?></th>
                            <th><?= __('Notes') ?></th>
                            <th><?= __('Marked By') ?></th>
                            <th><?= __('Created') ?></th>
                            <th><?= __('Modified') ?></th>
                            <th class="actions"><?= __('Actions') ?></th>
                        </tr>
                        <?php foreach ($student->attendance as $attendance) : ?>
                        <tr>
                            <td><?= h($attendance->id) ?></td>
                            <td><?= h($attendance->schedule_id) ?></td>
                            <td><?= h($attendance->student_id) ?></td>
                            <td><?= h($attendance->status) ?></td>
                            <td><?= h($attendance->check_in_time) ?></td>
                            <td><?= h($attendance->notes) ?></td>
                            <td><?= h($attendance->marked_by) ?></td>
                            <td><?= h($attendance->created) ?></td>
                            <td><?= h($attendance->modified) ?></td>
                            <td class="actions">
                                <?= $this->Html->link(__('View'), ['controller' => 'Attendance', 'action' => 'view', $attendance->id]) ?>
                                <?= $this->Html->link(__('Edit'), ['controller' => 'Attendance', 'action' => 'edit', $attendance->id]) ?>
                                <?= $this->Form->postLink(
                                    __('Delete'),
                                    ['controller' => 'Attendance', 'action' => 'delete', $attendance->id],
                                    [
                                        'method' => 'delete',
                                        'confirm' => __('Are you sure you want to delete # {0}?', $attendance->id),
                                    ]
                                ) ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                <?php endif; ?>
            </div>
            <div class="related">
                <h4><?= __('Related Finances') ?></h4>
                <?php if (!empty($student->finances)) : ?>
                <div class="table-responsive">
                    <table>
                        <tr>
                            <th><?= __('Id') ?></th>
                            <th><?= __('Type') ?></th>
                            <th><?= __('Category') ?></th>
                            <th><?= __('Description') ?></th>
                            <th><?= __('Amount') ?></th>
                            <th><?= __('Transaction Date') ?></th>
                            <th><?= __('Student Id') ?></th>
                            <th><?= __('Created By') ?></th>
                            <th><?= __('Created') ?></th>
                            <th><?= __('Modified') ?></th>
                            <th class="actions"><?= __('Actions') ?></th>
                        </tr>
                        <?php foreach ($student->finances as $finance) : ?>
                        <tr>
                            <td><?= h($finance->id) ?></td>
                            <td><?= h($finance->type) ?></td>
                            <td><?= h($finance->category) ?></td>
                            <td><?= h($finance->description) ?></td>
                            <td><?= h($finance->amount) ?></td>
                            <td><?= h($finance->transaction_date) ?></td>
                            <td><?= h($finance->student_id) ?></td>
                            <td><?= h($finance->created_by) ?></td>
                            <td><?= h($finance->created) ?></td>
                            <td><?= h($finance->modified) ?></td>
                            <td class="actions">
                                <?= $this->Html->link(__('View'), ['controller' => 'Finances', 'action' => 'view', $finance->id]) ?>
                                <?= $this->Html->link(__('Edit'), ['controller' => 'Finances', 'action' => 'edit', $finance->id]) ?>
                                <?= $this->Form->postLink(
                                    __('Delete'),
                                    ['controller' => 'Finances', 'action' => 'delete', $finance->id],
                                    [
                                        'method' => 'delete',
                                        'confirm' => __('Are you sure you want to delete # {0}?', $finance->id),
                                    ]
                                ) ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>