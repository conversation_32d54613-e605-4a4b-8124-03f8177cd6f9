<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Student $student
 * @var \Cake\Collection\CollectionInterface|string[] $classes
 */
?>
<!-- Page header -->
<div class="sm:flex sm:items-center sm:justify-between mb-8">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Thêm học sinh mới</h1>
        <p class="mt-2 text-gray-600">Nhập thông tin học sinh để đăng ký vào hệ thống</p>
    </div>
    <div class="mt-4 sm:mt-0">
        <?= $this->Html->link(
            '<i class="fas fa-arrow-left mr-2"></i>Quay lại danh sách',
            ['action' => 'index'],
            [
                'class' => 'inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500',
                'escape' => false
            ]
        ) ?>
    </div>
</div>

<!-- Form -->
<div class="bg-white shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">
            <i class="fas fa-user-plus mr-2 text-primary-600"></i>
            Thông tin học sinh
        </h2>
    </div>

    <div class="p-6">
        <?= $this->Form->create($student, [
            'class' => 'space-y-6'
        ]) ?>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Mã học sinh -->
            <div>
                <?= $this->Form->control('student_code', [
                    'label' => [
                        'text' => 'Mã học sinh',
                        'class' => 'block text-sm font-medium text-gray-700 mb-2'
                    ],
                    'class' => 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm',
                    'placeholder' => 'Nhập mã học sinh (tự động tạo nếu để trống)'
                ]) ?>
            </div>

            <!-- Họ và tên -->
            <div>
                <?= $this->Form->control('full_name', [
                    'label' => [
                        'text' => 'Họ và tên *',
                        'class' => 'block text-sm font-medium text-gray-700 mb-2'
                    ],
                    'class' => 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm',
                    'placeholder' => 'Nhập họ và tên học sinh',
                    'required' => true
                ]) ?>
            </div>

            <!-- Ngày sinh -->
            <div>
                <?= $this->Form->control('date_of_birth', [
                    'type' => 'date',
                    'label' => [
                        'text' => 'Ngày sinh',
                        'class' => 'block text-sm font-medium text-gray-700 mb-2'
                    ],
                    'class' => 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm'
                ]) ?>
            </div>

            <!-- Lớp học -->
            <div>
                <?= $this->Form->control('class_id', [
                    'type' => 'select',
                    'options' => $classes,
                    'empty' => 'Chọn lớp học',
                    'label' => [
                        'text' => 'Lớp học',
                        'class' => 'block text-sm font-medium text-gray-700 mb-2'
                    ],
                    'class' => 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm'
                ]) ?>
            </div>

            <!-- Tên phụ huynh -->
            <div>
                <?= $this->Form->control('parent_name', [
                    'label' => [
                        'text' => 'Tên phụ huynh',
                        'class' => 'block text-sm font-medium text-gray-700 mb-2'
                    ],
                    'class' => 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm',
                    'placeholder' => 'Nhập tên phụ huynh'
                ]) ?>
            </div>

            <!-- Số điện thoại phụ huynh -->
            <div>
                <?= $this->Form->control('parent_phone', [
                    'label' => [
                        'text' => 'Số điện thoại phụ huynh',
                        'class' => 'block text-sm font-medium text-gray-700 mb-2'
                    ],
                    'class' => 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm',
                    'placeholder' => 'Nhập số điện thoại'
                ]) ?>
            </div>
        </div>

        <!-- Google Drive Link -->
        <div>
            <?= $this->Form->control('google_drive_link', [
                'label' => [
                    'text' => 'Link Google Drive hồ sơ',
                    'class' => 'block text-sm font-medium text-gray-700 mb-2'
                ],
                'class' => 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm',
                'placeholder' => 'https://drive.google.com/...'
            ]) ?>
            <p class="mt-1 text-sm text-gray-500">
                <i class="fas fa-info-circle mr-1"></i>
                Link thư mục Google Drive chứa hồ sơ học sinh
            </p>
        </div>

        <!-- Trạng thái -->
        <div>
            <?= $this->Form->control('status', [
                'type' => 'select',
                'options' => [
                    'active' => 'Đang học',
                    'inactive' => 'Tạm nghỉ',
                    'graduated' => 'Đã tốt nghiệp'
                ],
                'default' => 'active',
                'label' => [
                    'text' => 'Trạng thái',
                    'class' => 'block text-sm font-medium text-gray-700 mb-2'
                ],
                'class' => 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm'
            ]) ?>
        </div>

        <!-- Buttons -->
        <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <?= $this->Html->link(
                'Hủy',
                ['action' => 'index'],
                [
                    'class' => 'inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                ]
            ) ?>
            <?= $this->Form->submit('Thêm học sinh', [
                'class' => 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
            ]) ?>
        </div>

        <?= $this->Form->end() ?>
    </div>
</div>
