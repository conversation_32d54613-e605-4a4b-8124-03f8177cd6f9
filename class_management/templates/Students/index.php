<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Student> $students
 */
?>
<!-- Page header -->
<div class="sm:flex sm:items-center sm:justify-between mb-8">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Quản lý học sinh</h1>
        <p class="mt-2 text-gray-600">Danh sách tất cả học sinh trong hệ thống</p>
    </div>
    <div class="mt-4 sm:mt-0">
        <?= $this->Html->link(
            '<i class="fas fa-plus mr-2"></i>Thêm học sinh mới',
            ['action' => 'add'],
            [
                'class' => 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500',
                'escape' => false
            ]
        ) ?>
    </div>
</div>

<!-- Students table -->
<div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <?= $this->Paginator->sort('student_code', 'Mã học sinh') ?>
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <?= $this->Paginator->sort('full_name', 'Họ và tên') ?>
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <?= $this->Paginator->sort('date_of_birth', 'Ngày sinh') ?>
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Lớp học
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Phụ huynh
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <?= $this->Paginator->sort('status', 'Trạng thái') ?>
                    </th>
                    <th scope="col" class="relative px-6 py-3">
                        <span class="sr-only">Thao tác</span>
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php foreach ($students as $student): ?>
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-8 w-8">
                                <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                                    <i class="fas fa-user-graduate text-blue-600 text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900"><?= h($student->student_code) ?></div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900"><?= h($student->full_name) ?></div>
                        <div class="text-sm text-gray-500">ID: <?= $this->Number->format($student->id) ?></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <?= h($student->date_of_birth ? $student->date_of_birth->format('d/m/Y') : '') ?>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <?php if ($student->hasValue('class')): ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                <?= h($student->class->name) ?>
                            </span>
                        <?php else: ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                Chưa phân lớp
                            </span>
                        <?php endif; ?>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div class="text-sm font-medium text-gray-900"><?= h($student->parent_name) ?></div>
                        <div class="text-sm text-gray-500"><?= h($student->parent_phone) ?></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <?php
                        $statusColors = [
                            'active' => 'bg-green-100 text-green-800',
                            'inactive' => 'bg-gray-100 text-gray-800',
                            'graduated' => 'bg-blue-100 text-blue-800'
                        ];
                        $statusNames = [
                            'active' => 'Đang học',
                            'inactive' => 'Tạm nghỉ',
                            'graduated' => 'Đã tốt nghiệp'
                        ];
                        ?>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?= $statusColors[$student->status] ?? 'bg-gray-100 text-gray-800' ?>">
                            <?= $statusNames[$student->status] ?? $student->status ?>
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center space-x-2">
                            <?= $this->Html->link(
                                '<i class="fas fa-eye"></i>',
                                ['action' => 'view', $student->id],
                                [
                                    'class' => 'text-primary-600 hover:text-primary-900 p-1',
                                    'escape' => false,
                                    'title' => 'Xem chi tiết'
                                ]
                            ) ?>
                            <?= $this->Html->link(
                                '<i class="fas fa-edit"></i>',
                                ['action' => 'edit', $student->id],
                                [
                                    'class' => 'text-yellow-600 hover:text-yellow-900 p-1',
                                    'escape' => false,
                                    'title' => 'Chỉnh sửa'
                                ]
                            ) ?>
                            <?= $this->Form->postLink(
                                '<i class="fas fa-trash"></i>',
                                ['action' => 'delete', $student->id],
                                [
                                    'confirm' => __('Bạn có chắc muốn xóa học sinh {0}?', $student->full_name),
                                    'class' => 'text-red-600 hover:text-red-900 p-1',
                                    'escape' => false,
                                    'title' => 'Xóa'
                                ]
                            ) ?>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
            <?= $this->Paginator->prev('Trước', [
                'class' => 'relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50'
            ]) ?>
            <?= $this->Paginator->next('Sau', [
                'class' => 'ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50'
            ]) ?>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700">
                    <?= $this->Paginator->counter('Hiển thị {{start}} đến {{end}} trong tổng số {{count}} học sinh') ?>
                </p>
            </div>
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <?= $this->Paginator->first('<<', [
                        'class' => 'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50'
                    ]) ?>
                    <?= $this->Paginator->prev('<', [
                        'class' => 'relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50'
                    ]) ?>
                    <?= $this->Paginator->numbers([
                        'class' => 'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50',
                        'currentClass' => 'relative inline-flex items-center px-4 py-2 border border-primary-500 bg-primary-50 text-sm font-medium text-primary-600'
                    ]) ?>
                    <?= $this->Paginator->next('>', [
                        'class' => 'relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50'
                    ]) ?>
                    <?= $this->Paginator->last('>>', [
                        'class' => 'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50'
                    ]) ?>
                </nav>
            </div>
        </div>
    </div>
</div>