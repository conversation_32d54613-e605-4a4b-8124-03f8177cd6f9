<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Finance $finance
 * @var \Cake\Collection\CollectionInterface|string[] $students
 */
?>
<div class="row">
    <aside class="column">
        <div class="side-nav">
            <h4 class="heading"><?= __('Actions') ?></h4>
            <?= $this->Html->link(__('List Finances'), ['action' => 'index'], ['class' => 'side-nav-item']) ?>
        </div>
    </aside>
    <div class="column column-80">
        <div class="finances form content">
            <?= $this->Form->create($finance) ?>
            <fieldset>
                <legend><?= __('Add Finance') ?></legend>
                <?php
                    echo $this->Form->control('type');
                    echo $this->Form->control('category');
                    echo $this->Form->control('description');
                    echo $this->Form->control('amount');
                    echo $this->Form->control('transaction_date');
                    echo $this->Form->control('student_id', ['options' => $students, 'empty' => true]);
                    echo $this->Form->control('created_by');
                ?>
            </fieldset>
            <?= $this->Form->button(__('Submit')) ?>
            <?= $this->Form->end() ?>
        </div>
    </div>
</div>
