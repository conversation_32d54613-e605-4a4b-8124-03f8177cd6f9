<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Finance> $finances
 */
?>
<div class="finances index content">
    <?= $this->Html->link(__('New Finance'), ['action' => 'add'], ['class' => 'button float-right']) ?>
    <h3><?= __('Finances') ?></h3>
    <div class="table-responsive">
        <table>
            <thead>
                <tr>
                    <th><?= $this->Paginator->sort('id') ?></th>
                    <th><?= $this->Paginator->sort('type') ?></th>
                    <th><?= $this->Paginator->sort('category') ?></th>
                    <th><?= $this->Paginator->sort('amount') ?></th>
                    <th><?= $this->Paginator->sort('transaction_date') ?></th>
                    <th><?= $this->Paginator->sort('student_id') ?></th>
                    <th><?= $this->Paginator->sort('created_by') ?></th>
                    <th><?= $this->Paginator->sort('created') ?></th>
                    <th><?= $this->Paginator->sort('modified') ?></th>
                    <th class="actions"><?= __('Actions') ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($finances as $finance): ?>
                <tr>
                    <td><?= $this->Number->format($finance->id) ?></td>
                    <td><?= h($finance->type) ?></td>
                    <td><?= h($finance->category) ?></td>
                    <td><?= $this->Number->format($finance->amount) ?></td>
                    <td><?= h($finance->transaction_date) ?></td>
                    <td><?= $finance->hasValue('student') ? $this->Html->link($finance->student->student_code, ['controller' => 'Students', 'action' => 'view', $finance->student->id]) : '' ?></td>
                    <td><?= $this->Number->format($finance->created_by) ?></td>
                    <td><?= h($finance->created) ?></td>
                    <td><?= h($finance->modified) ?></td>
                    <td class="actions">
                        <?= $this->Html->link(__('View'), ['action' => 'view', $finance->id]) ?>
                        <?= $this->Html->link(__('Edit'), ['action' => 'edit', $finance->id]) ?>
                        <?= $this->Form->postLink(
                            __('Delete'),
                            ['action' => 'delete', $finance->id],
                            [
                                'method' => 'delete',
                                'confirm' => __('Are you sure you want to delete # {0}?', $finance->id),
                            ]
                        ) ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <div class="paginator">
        <ul class="pagination">
            <?= $this->Paginator->first('<< ' . __('first')) ?>
            <?= $this->Paginator->prev('< ' . __('previous')) ?>
            <?= $this->Paginator->numbers() ?>
            <?= $this->Paginator->next(__('next') . ' >') ?>
            <?= $this->Paginator->last(__('last') . ' >>') ?>
        </ul>
        <p><?= $this->Paginator->counter(__('Page {{page}} of {{pages}}, showing {{current}} record(s) out of {{count}} total')) ?></p>
    </div>
</div>