<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Asset> $assets
 */
?>
<div class="assets index content">
    <?= $this->Html->link(__('New Asset'), ['action' => 'add'], ['class' => 'button float-right']) ?>
    <h3><?= __('Assets') ?></h3>
    <div class="table-responsive">
        <table>
            <thead>
                <tr>
                    <th><?= $this->Paginator->sort('id') ?></th>
                    <th><?= $this->Paginator->sort('name') ?></th>
                    <th><?= $this->Paginator->sort('category') ?></th>
                    <th><?= $this->Paginator->sort('quantity') ?></th>
                    <th><?= $this->Paginator->sort('unit_price') ?></th>
                    <th><?= $this->Paginator->sort('total_value') ?></th>
                    <th><?= $this->Paginator->sort('purchase_date') ?></th>
                    <th><?= $this->Paginator->sort('assigned_to') ?></th>
                    <th><?= $this->Paginator->sort('status') ?></th>
                    <th><?= $this->Paginator->sort('created_by') ?></th>
                    <th><?= $this->Paginator->sort('created') ?></th>
                    <th><?= $this->Paginator->sort('modified') ?></th>
                    <th class="actions"><?= __('Actions') ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($assets as $asset): ?>
                <tr>
                    <td><?= $this->Number->format($asset->id) ?></td>
                    <td><?= h($asset->name) ?></td>
                    <td><?= h($asset->category) ?></td>
                    <td><?= $this->Number->format($asset->quantity) ?></td>
                    <td><?= $asset->unit_price === null ? '' : $this->Number->format($asset->unit_price) ?></td>
                    <td><?= $asset->total_value === null ? '' : $this->Number->format($asset->total_value) ?></td>
                    <td><?= h($asset->purchase_date) ?></td>
                    <td><?= $asset->assigned_to === null ? '' : $this->Number->format($asset->assigned_to) ?></td>
                    <td><?= h($asset->status) ?></td>
                    <td><?= $this->Number->format($asset->created_by) ?></td>
                    <td><?= h($asset->created) ?></td>
                    <td><?= h($asset->modified) ?></td>
                    <td class="actions">
                        <?= $this->Html->link(__('View'), ['action' => 'view', $asset->id]) ?>
                        <?= $this->Html->link(__('Edit'), ['action' => 'edit', $asset->id]) ?>
                        <?= $this->Form->postLink(
                            __('Delete'),
                            ['action' => 'delete', $asset->id],
                            [
                                'method' => 'delete',
                                'confirm' => __('Are you sure you want to delete # {0}?', $asset->id),
                            ]
                        ) ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <div class="paginator">
        <ul class="pagination">
            <?= $this->Paginator->first('<< ' . __('first')) ?>
            <?= $this->Paginator->prev('< ' . __('previous')) ?>
            <?= $this->Paginator->numbers() ?>
            <?= $this->Paginator->next(__('next') . ' >') ?>
            <?= $this->Paginator->last(__('last') . ' >>') ?>
        </ul>
        <p><?= $this->Paginator->counter(__('Page {{page}} of {{pages}}, showing {{current}} record(s) out of {{count}} total')) ?></p>
    </div>
</div>