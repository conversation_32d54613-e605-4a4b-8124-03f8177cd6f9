<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\TimeSlot $timeSlot
 */
?>
<div class="row">
    <aside class="column">
        <div class="side-nav">
            <h4 class="heading"><?= __('Actions') ?></h4>
            <?= $this->Html->link(__('Edit Time Slot'), ['action' => 'edit', $timeSlot->id], ['class' => 'side-nav-item']) ?>
            <?= $this->Form->postLink(__('Delete Time Slot'), ['action' => 'delete', $timeSlot->id], ['confirm' => __('Are you sure you want to delete # {0}?', $timeSlot->id), 'class' => 'side-nav-item']) ?>
            <?= $this->Html->link(__('List Time Slots'), ['action' => 'index'], ['class' => 'side-nav-item']) ?>
            <?= $this->Html->link(__('New Time Slot'), ['action' => 'add'], ['class' => 'side-nav-item']) ?>
        </div>
    </aside>
    <div class="column column-80">
        <div class="timeSlots view content">
            <h3><?= h($timeSlot->name) ?></h3>
            <table>
                <tr>
                    <th><?= __('Name') ?></th>
                    <td><?= h($timeSlot->name) ?></td>
                </tr>
                <tr>
                    <th><?= __('Period') ?></th>
                    <td><?= h($timeSlot->period) ?></td>
                </tr>
                <tr>
                    <th><?= __('Status') ?></th>
                    <td><?= h($timeSlot->status) ?></td>
                </tr>
                <tr>
                    <th><?= __('Id') ?></th>
                    <td><?= $this->Number->format($timeSlot->id) ?></td>
                </tr>
                <tr>
                    <th><?= __('Start Time') ?></th>
                    <td><?= h($timeSlot->start_time) ?></td>
                </tr>
                <tr>
                    <th><?= __('End Time') ?></th>
                    <td><?= h($timeSlot->end_time) ?></td>
                </tr>
                <tr>
                    <th><?= __('Created') ?></th>
                    <td><?= h($timeSlot->created) ?></td>
                </tr>
                <tr>
                    <th><?= __('Modified') ?></th>
                    <td><?= h($timeSlot->modified) ?></td>
                </tr>
            </table>
            <div class="related">
                <h4><?= __('Related Schedules') ?></h4>
                <?php if (!empty($timeSlot->schedules)) : ?>
                <div class="table-responsive">
                    <table>
                        <tr>
                            <th><?= __('Id') ?></th>
                            <th><?= __('Class Id') ?></th>
                            <th><?= __('Time Slot Id') ?></th>
                            <th><?= __('Teacher Id') ?></th>
                            <th><?= __('Schedule Date') ?></th>
                            <th><?= __('Status') ?></th>
                            <th><?= __('Notes') ?></th>
                            <th><?= __('Created') ?></th>
                            <th><?= __('Modified') ?></th>
                            <th class="actions"><?= __('Actions') ?></th>
                        </tr>
                        <?php foreach ($timeSlot->schedules as $schedule) : ?>
                        <tr>
                            <td><?= h($schedule->id) ?></td>
                            <td><?= h($schedule->class_id) ?></td>
                            <td><?= h($schedule->time_slot_id) ?></td>
                            <td><?= h($schedule->teacher_id) ?></td>
                            <td><?= h($schedule->schedule_date) ?></td>
                            <td><?= h($schedule->status) ?></td>
                            <td><?= h($schedule->notes) ?></td>
                            <td><?= h($schedule->created) ?></td>
                            <td><?= h($schedule->modified) ?></td>
                            <td class="actions">
                                <?= $this->Html->link(__('View'), ['controller' => 'Schedules', 'action' => 'view', $schedule->id]) ?>
                                <?= $this->Html->link(__('Edit'), ['controller' => 'Schedules', 'action' => 'edit', $schedule->id]) ?>
                                <?= $this->Form->postLink(
                                    __('Delete'),
                                    ['controller' => 'Schedules', 'action' => 'delete', $schedule->id],
                                    [
                                        'method' => 'delete',
                                        'confirm' => __('Are you sure you want to delete # {0}?', $schedule->id),
                                    ]
                                ) ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>