<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\TimeSlot> $timeSlots
 */
?>
<div class="timeSlots index content">
    <?= $this->Html->link(__('New Time Slot'), ['action' => 'add'], ['class' => 'button float-right']) ?>
    <h3><?= __('Time Slots') ?></h3>
    <div class="table-responsive">
        <table>
            <thead>
                <tr>
                    <th><?= $this->Paginator->sort('id') ?></th>
                    <th><?= $this->Paginator->sort('name') ?></th>
                    <th><?= $this->Paginator->sort('period') ?></th>
                    <th><?= $this->Paginator->sort('start_time') ?></th>
                    <th><?= $this->Paginator->sort('end_time') ?></th>
                    <th><?= $this->Paginator->sort('status') ?></th>
                    <th><?= $this->Paginator->sort('created') ?></th>
                    <th><?= $this->Paginator->sort('modified') ?></th>
                    <th class="actions"><?= __('Actions') ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($timeSlots as $timeSlot): ?>
                <tr>
                    <td><?= $this->Number->format($timeSlot->id) ?></td>
                    <td><?= h($timeSlot->name) ?></td>
                    <td><?= h($timeSlot->period) ?></td>
                    <td><?= h($timeSlot->start_time) ?></td>
                    <td><?= h($timeSlot->end_time) ?></td>
                    <td><?= h($timeSlot->status) ?></td>
                    <td><?= h($timeSlot->created) ?></td>
                    <td><?= h($timeSlot->modified) ?></td>
                    <td class="actions">
                        <?= $this->Html->link(__('View'), ['action' => 'view', $timeSlot->id]) ?>
                        <?= $this->Html->link(__('Edit'), ['action' => 'edit', $timeSlot->id]) ?>
                        <?= $this->Form->postLink(
                            __('Delete'),
                            ['action' => 'delete', $timeSlot->id],
                            [
                                'method' => 'delete',
                                'confirm' => __('Are you sure you want to delete # {0}?', $timeSlot->id),
                            ]
                        ) ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <div class="paginator">
        <ul class="pagination">
            <?= $this->Paginator->first('<< ' . __('first')) ?>
            <?= $this->Paginator->prev('< ' . __('previous')) ?>
            <?= $this->Paginator->numbers() ?>
            <?= $this->Paginator->next(__('next') . ' >') ?>
            <?= $this->Paginator->last(__('last') . ' >>') ?>
        </ul>
        <p><?= $this->Paginator->counter(__('Page {{page}} of {{pages}}, showing {{current}} record(s) out of {{count}} total')) ?></p>
    </div>
</div>