<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Schedule $schedule
 */
?>
<div class="row">
    <aside class="column">
        <div class="side-nav">
            <h4 class="heading"><?= __('Actions') ?></h4>
            <?= $this->Html->link(__('Edit Schedule'), ['action' => 'edit', $schedule->id], ['class' => 'side-nav-item']) ?>
            <?= $this->Form->postLink(__('Delete Schedule'), ['action' => 'delete', $schedule->id], ['confirm' => __('Are you sure you want to delete # {0}?', $schedule->id), 'class' => 'side-nav-item']) ?>
            <?= $this->Html->link(__('List Schedules'), ['action' => 'index'], ['class' => 'side-nav-item']) ?>
            <?= $this->Html->link(__('New Schedule'), ['action' => 'add'], ['class' => 'side-nav-item']) ?>
        </div>
    </aside>
    <div class="column column-80">
        <div class="schedules view content">
            <h3><?= h($schedule->status) ?></h3>
            <table>
                <tr>
                    <th><?= __('Class') ?></th>
                    <td><?= $schedule->hasValue('class') ? $this->Html->link($schedule->class->name, ['controller' => 'Classes', 'action' => 'view', $schedule->class->id]) : '' ?></td>
                </tr>
                <tr>
                    <th><?= __('Time Slot') ?></th>
                    <td><?= $schedule->hasValue('time_slot') ? $this->Html->link($schedule->time_slot->name, ['controller' => 'TimeSlots', 'action' => 'view', $schedule->time_slot->id]) : '' ?></td>
                </tr>
                <tr>
                    <th><?= __('Teacher') ?></th>
                    <td><?= $schedule->hasValue('teacher') ? $this->Html->link($schedule->teacher->username, ['controller' => 'Users', 'action' => 'view', $schedule->teacher->id]) : '' ?></td>
                </tr>
                <tr>
                    <th><?= __('Status') ?></th>
                    <td><?= h($schedule->status) ?></td>
                </tr>
                <tr>
                    <th><?= __('Id') ?></th>
                    <td><?= $this->Number->format($schedule->id) ?></td>
                </tr>
                <tr>
                    <th><?= __('Schedule Date') ?></th>
                    <td><?= h($schedule->schedule_date) ?></td>
                </tr>
                <tr>
                    <th><?= __('Created') ?></th>
                    <td><?= h($schedule->created) ?></td>
                </tr>
                <tr>
                    <th><?= __('Modified') ?></th>
                    <td><?= h($schedule->modified) ?></td>
                </tr>
            </table>
            <div class="text">
                <strong><?= __('Notes') ?></strong>
                <blockquote>
                    <?= $this->Text->autoParagraph(h($schedule->notes)); ?>
                </blockquote>
            </div>
            <div class="related">
                <h4><?= __('Related Attendance') ?></h4>
                <?php if (!empty($schedule->attendance)) : ?>
                <div class="table-responsive">
                    <table>
                        <tr>
                            <th><?= __('Id') ?></th>
                            <th><?= __('Schedule Id') ?></th>
                            <th><?= __('Student Id') ?></th>
                            <th><?= __('Status') ?></th>
                            <th><?= __('Check In Time') ?></th>
                            <th><?= __('Notes') ?></th>
                            <th><?= __('Marked By') ?></th>
                            <th><?= __('Created') ?></th>
                            <th><?= __('Modified') ?></th>
                            <th class="actions"><?= __('Actions') ?></th>
                        </tr>
                        <?php foreach ($schedule->attendance as $attendance) : ?>
                        <tr>
                            <td><?= h($attendance->id) ?></td>
                            <td><?= h($attendance->schedule_id) ?></td>
                            <td><?= h($attendance->student_id) ?></td>
                            <td><?= h($attendance->status) ?></td>
                            <td><?= h($attendance->check_in_time) ?></td>
                            <td><?= h($attendance->notes) ?></td>
                            <td><?= h($attendance->marked_by) ?></td>
                            <td><?= h($attendance->created) ?></td>
                            <td><?= h($attendance->modified) ?></td>
                            <td class="actions">
                                <?= $this->Html->link(__('View'), ['controller' => 'Attendance', 'action' => 'view', $attendance->id]) ?>
                                <?= $this->Html->link(__('Edit'), ['controller' => 'Attendance', 'action' => 'edit', $attendance->id]) ?>
                                <?= $this->Form->postLink(
                                    __('Delete'),
                                    ['controller' => 'Attendance', 'action' => 'delete', $attendance->id],
                                    [
                                        'method' => 'delete',
                                        'confirm' => __('Are you sure you want to delete # {0}?', $attendance->id),
                                    ]
                                ) ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                <?php endif; ?>
            </div>
            <div class="related">
                <h4><?= __('Related Notifications') ?></h4>
                <?php if (!empty($schedule->notifications)) : ?>
                <div class="table-responsive">
                    <table>
                        <tr>
                            <th><?= __('Id') ?></th>
                            <th><?= __('Title') ?></th>
                            <th><?= __('Message') ?></th>
                            <th><?= __('Type') ?></th>
                            <th><?= __('Target Audience') ?></th>
                            <th><?= __('Target User Id') ?></th>
                            <th><?= __('Schedule Id') ?></th>
                            <th><?= __('Is Read') ?></th>
                            <th><?= __('Sent By') ?></th>
                            <th><?= __('Sent At') ?></th>
                            <th><?= __('Created') ?></th>
                            <th><?= __('Modified') ?></th>
                            <th class="actions"><?= __('Actions') ?></th>
                        </tr>
                        <?php foreach ($schedule->notifications as $notification) : ?>
                        <tr>
                            <td><?= h($notification->id) ?></td>
                            <td><?= h($notification->title) ?></td>
                            <td><?= h($notification->message) ?></td>
                            <td><?= h($notification->type) ?></td>
                            <td><?= h($notification->target_audience) ?></td>
                            <td><?= h($notification->target_user_id) ?></td>
                            <td><?= h($notification->schedule_id) ?></td>
                            <td><?= h($notification->is_read) ?></td>
                            <td><?= h($notification->sent_by) ?></td>
                            <td><?= h($notification->sent_at) ?></td>
                            <td><?= h($notification->created) ?></td>
                            <td><?= h($notification->modified) ?></td>
                            <td class="actions">
                                <?= $this->Html->link(__('View'), ['controller' => 'Notifications', 'action' => 'view', $notification->id]) ?>
                                <?= $this->Html->link(__('Edit'), ['controller' => 'Notifications', 'action' => 'edit', $notification->id]) ?>
                                <?= $this->Form->postLink(
                                    __('Delete'),
                                    ['controller' => 'Notifications', 'action' => 'delete', $notification->id],
                                    [
                                        'method' => 'delete',
                                        'confirm' => __('Are you sure you want to delete # {0}?', $notification->id),
                                    ]
                                ) ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>