name: Prepare Release

on:
  workflow_dispatch:
  push:
    tags:
      - 'v*'

permissions:
  contents: read

jobs:
  prepare:
    permissions:
      contents: write # for softprops/action-gh-release to create GitHub release

    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [22]

    steps:
      - uses: actions/checkout@v4

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: 'https://registry.npmjs.org'
          cache: 'npm'

      - name: Install dependencies
        run: npm install

      - name: Test
        run: npm test

      - name: Resolve version
        id: vars
        run: |
          echo "TAG_NAME=$(git describe --tags --abbrev=0)" >> $GITHUB_ENV

      - name: Get release notes
        run: |
          RELEASE_NOTES=$(npm run release-notes --silent)
          echo "RELEASE_NOTES<<EOF" >> $GITHUB_ENV
          echo "$RELEASE_NOTES" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - name: Release
        uses: softprops/action-gh-release@v1
        with:
          draft: true
          tag_name: ${{ env.TAG_NAME }}
          body: ${{ env.RELEASE_NOTES }}
