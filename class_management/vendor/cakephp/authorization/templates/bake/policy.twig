{#
/**
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @since         1.0.0
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 */
#}
<?php
declare(strict_types=1);

namespace {{ namespace }}\Policy;

{% for import in imports|sort %}
use {{ import }};
{% endfor %}

/**
 * {{ name }} policy
 */
class {{ name }}{{ suffix }}Policy
{
{% if type == 'entity' %}
    {{- _view.element('Authorization.entity_methods') -}}
{% elseif type == 'table' %}
    {{- _view.element('Authorization.table_methods') -}}
{% endif %}
}
