    /**
     * Check if $user can add {{ name }}
     *
     * @param \Authorization\IdentityInterface $user The user.
     * @param \{{ classname }} ${{ variable_name }}
     * @return bool
     */
    public function canAdd(IdentityInterface $user, {{ name }} ${{ variable_name }})
    {
    }

    /**
     * Check if $user can edit {{ name }}
     *
     * @param \Authorization\IdentityInterface $user The user.
     * @param \{{ classname }} ${{ variable_name }}
     * @return bool
     */
    public function canEdit(IdentityInterface $user, {{ name }} ${{ variable_name }})
    {
    }

    /**
     * Check if $user can delete {{ name }}
     *
     * @param \Authorization\IdentityInterface $user The user.
     * @param \{{ classname }} ${{ variable_name }}
     * @return bool
     */
    public function canDelete(IdentityInterface $user, {{ name }} ${{ variable_name }})
    {
    }

    /**
     * Check if $user can view {{ name }}
     *
     * @param \Authorization\IdentityInterface $user The user.
     * @param \{{ classname }} ${{ variable_name }}
     * @return bool
     */
    public function canView(IdentityInterface $user, {{ name }} ${{ variable_name }})
    {
    }
