{"name": "cakephp/authorization", "description": "Authorization abstraction layer plugin for CakePHP", "license": "MIT", "type": "cakephp-plugin", "keywords": ["auth", "authorization", "access", "cakephp"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/authorization/graphs/contributors"}], "support": {"issues": "https://github.com/cakephp/authorization/issues", "forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "source": "https://github.com/cakephp/authorization", "docs": "https://cakephp.org/authorization/2/en/"}, "require": {"php": ">=8.1", "cakephp/http": "^5.1", "psr/http-client": "^1.0", "psr/http-message": "^1.1 || ^2.0", "psr/http-server-handler": "^1.0", "psr/http-server-middleware": "^1.0"}, "require-dev": {"cakephp/authentication": "^3.0", "cakephp/bake": "^3.2", "cakephp/cakephp": "^5.1", "cakephp/cakephp-codesniffer": "^5.1", "phpunit/phpunit": "^10.5.5 || ^11.1.3"}, "suggest": {"cakephp/http": "To use \"RequestPolicyInterface\" (Not needed separately if using full CakePHP framework).", "cakephp/orm": "To use \"OrmResolver\" (Not needed separately if using full CakePHP framework)."}, "autoload": {"psr-4": {"Authorization\\": "src/"}}, "autoload-dev": {"psr-4": {"Authorization\\Test\\": "tests/", "OverridePlugin\\": "tests/test_app/Plugin/OverridePlugin/src/", "TestApp\\": "tests/test_app/TestApp/", "TestPlugin\\": "tests/test_app/Plugin/TestPlugin/src/"}}, "config": {"allow-plugins": {"cakephp/plugin-installer": true, "dealerdirect/phpcodesniffer-composer-installer": true}, "sort-packages": true}, "scripts": {"check": ["@cs-check", "@stan", "@test"], "cs-check": "phpcs --colors -p src/ tests/", "cs-fix": "phpcbf --colors -p src/ tests/", "phpstan": "tools/phpstan analyse", "psalm": "tools/psalm --show-info=false", "psalm-baseline": "tools/psalm  --set-baseline=psalm-baseline.xml", "stan": ["@phpstan", "@psalm"], "stan-baseline": "tools/phpstan --generate-baseline", "stan-setup": "phive install", "test": "phpunit", "test-coverage": "phpunit --coverage-clover=clover.xml"}, "minimum-stability": "dev", "prefer-stable": true}