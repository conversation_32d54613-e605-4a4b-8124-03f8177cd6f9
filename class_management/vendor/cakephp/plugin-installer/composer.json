{"name": "cakephp/plugin-installer", "description": "A composer installer for CakePHP 3.0+ plugins.", "type": "composer-plugin", "license": "MIT", "authors": [{"name": "CakePHP Community", "homepage": "https://cakephp.org"}], "require": {"php": ">=8.1", "composer-plugin-api": "^2.0"}, "require-dev": {"cakephp/cakephp-codesniffer": "^5.0", "composer/composer": "^2.0", "phpunit/phpunit": "^10.1.0"}, "autoload": {"psr-4": {"Cake\\Composer\\": "src/"}}, "autoload-dev": {"psr-4": {"Cake\\Test\\TestCase\\Composer\\": "tests/TestCase/"}}, "extra": {"class": "Cake\\Composer\\Plugin"}, "config": {"sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "prefer-stable": true, "scripts": {"check": ["@cs-check", "@test"], "cs-check": "phpcs --colors --parallel=16 -p src/ tests/", "cs-fix": "phpcbf --colors --parallel=16 -p src/ tests/", "phpstan": "tools/phpstan analyse", "psalm": "tools/psalm --show-info=false", "stan": ["@phpstan", "@psalm"], "stan-tests": "phpstan.phar analyze -c tests/phpstan.neon", "stan-baseline": "phpstan.phar --generate-baseline", "stan-setup": "phive install", "test": "phpunit"}}