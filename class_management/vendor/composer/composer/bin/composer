#!/usr/bin/env php
<?php

if (PHP_SAPI !== 'cli' && PHP_SAPI !== 'phpdbg') {
    if (0 === strpos(__FILE__, 'phar:') && ini_get('register_argc_argv')) {
        echo 'Composer cannot be run safely on non-CLI SAPIs with register_argc_argv=On. Aborting.'.PHP_EOL;
        exit(1);
    }

    echo 'Warning: Composer should be invoked via the CLI version of PHP, not the '.PHP_SAPI.' SAPI'.PHP_EOL;
}

if (PHP_VERSION_ID < 70205) {
    echo 'Composer 2.3.0 dropped support for PHP <7.2.5 and you are running '.PHP_VERSION.', please upgrade PHP or use Composer 2.2 LTS via "composer self-update --2.2". Aborting.'.PHP_EOL;
    exit(1);
}

setlocale(LC_ALL, 'C');
require __DIR__.'/../src/bootstrap.php';

use Composer\Console\Application;
use Composer\XdebugHandler\XdebugHandler;
use Composer\Util\Platform;
use Composer\Util\ErrorHandler;

error_reporting(-1);

// Restart without Xdebug
$xdebug = new XdebugHandler('Composer');
$xdebug->check();
unset($xdebug);

if (defined('HHVM_VERSION') && version_compare(HHVM_VERSION, '4.0', '>=')) {
    echo 'HHVM 4.0 has dropped support for Composer, please use PHP instead. Aborting.'.PHP_EOL;
    exit(1);
}
if (!extension_loaded('iconv') && !extension_loaded('mbstring')) {
    echo 'The iconv OR mbstring extension is required and both are missing.'
        .PHP_EOL.'Install either of them or recompile php without --disable-iconv.'
        .PHP_EOL.'Aborting.'.PHP_EOL;
    exit(1);
}

if (function_exists('ini_set')) {
    // check if error logging is on, but to an empty destination - for the CLI SAPI, that means stderr
    $logsToSapiDefault = ('' === ini_get('error_log') && (bool) ini_get('log_errors'));
    // on the CLI SAPI, ensure errors are displayed on stderr, either via display_errors or via error_log
    if (PHP_SAPI === 'cli') {
        @ini_set('display_errors', $logsToSapiDefault ? '0' : 'stderr');
    }

    // Set user defined memory limit
    if ($memoryLimit = getenv('COMPOSER_MEMORY_LIMIT')) {
        @ini_set('memory_limit', $memoryLimit);
    } else {
        $memoryInBytes = function ($value) {
            $unit = strtolower(substr($value, -1, 1));
            $value = (int) $value;
            switch($unit) {
                case 'g':
                    $value *= 1024;
                    // no break (cumulative multiplier)
                case 'm':
                    $value *= 1024;
                    // no break (cumulative multiplier)
                case 'k':
                    $value *= 1024;
            }

            return $value;
        };

        $memoryLimit = trim(ini_get('memory_limit'));
        // Increase memory_limit if it is lower than 1.5GB
        if ($memoryLimit != -1 && $memoryInBytes($memoryLimit) < 1024 * 1024 * 1536) {
            @ini_set('memory_limit', '1536M');
        }
        unset($memoryInBytes);
    }
    unset($memoryLimit);
}

// Workaround PHP bug on Windows where env vars containing Unicode chars are mangled in $_SERVER
// see https://github.com/php/php-src/issues/7896
if (PHP_VERSION_ID >= 70113 && (PHP_VERSION_ID < 80016 || (PHP_VERSION_ID >= 80100 && PHP_VERSION_ID < 80103)) && Platform::isWindows()) {
    foreach ($_SERVER as $serverVar => $serverVal) {
        if (($serverVal = getenv($serverVar)) !== false) {
            $_SERVER[$serverVar] = $serverVal;
        }
    }
}

Platform::putEnv('COMPOSER_BINARY', realpath($_SERVER['argv'][0]));

ErrorHandler::register();

// run the command application
$application = new Application();
$application->run();
