<?php return array(
    'root' => array(
        'name' => 'cakephp/app',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'brick/varexporter' => array(
            'pretty_version' => '0.6.0',
            'version' => '0.6.0.0',
            'reference' => 'af98bfc2b702a312abbcaff37656dbe419cec5bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/varexporter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'cakephp/app' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cakephp/authentication' => array(
            'pretty_version' => '3.2.5',
            'version' => '3.2.5.0',
            'reference' => '445aef3af6350cad22f19530882cf5f758d69134',
            'type' => 'cakephp-plugin',
            'install_path' => __DIR__ . '/../cakephp/authentication',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cakephp/authorization' => array(
            'pretty_version' => '3.4.1',
            'version' => '3.4.1.0',
            'reference' => 'ff151d694b592a683350f420dd9e58b7c0a762c4',
            'type' => 'cakephp-plugin',
            'install_path' => __DIR__ . '/../cakephp/authorization',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cakephp/bake' => array(
            'pretty_version' => '3.3.1',
            'version' => '3.3.1.0',
            'reference' => '91d32beebfc63acc15308b0663acf0e288d2a9d5',
            'type' => 'cakephp-plugin',
            'install_path' => __DIR__ . '/../cakephp/bake',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'cakephp/cache' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '5.1.6',
            ),
        ),
        'cakephp/cakephp' => array(
            'pretty_version' => '5.1.6',
            'version' => '5.1.6.0',
            'reference' => '4b8915cf32949cac5c798af2ff65f688c4d76d21',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cakephp/cakephp',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cakephp/cakephp-codesniffer' => array(
            'pretty_version' => '5.2.2',
            'version' => '5.2.2.0',
            'reference' => '3c62979556e98d0330f8af8b103ad1ade0a7067e',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../cakephp/cakephp-codesniffer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'cakephp/chronos' => array(
            'pretty_version' => '3.1.0',
            'version' => '3.1.0.0',
            'reference' => '786d69e1ee4b735765cbdb5521b9603e9b98d650',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cakephp/chronos',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cakephp/collection' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '5.1.6',
            ),
        ),
        'cakephp/console' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '5.1.6',
            ),
        ),
        'cakephp/core' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '5.1.6',
            ),
        ),
        'cakephp/database' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '5.1.6',
            ),
        ),
        'cakephp/datasource' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '5.1.6',
            ),
        ),
        'cakephp/debug_kit' => array(
            'pretty_version' => '5.1.2',
            'version' => '5.1.2.0',
            'reference' => 'b83ec9e0c62480f7cc9639e01cb9310bb7f0816d',
            'type' => 'cakephp-plugin',
            'install_path' => __DIR__ . '/../cakephp/debug_kit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'cakephp/event' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '5.1.6',
            ),
        ),
        'cakephp/form' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '5.1.6',
            ),
        ),
        'cakephp/http' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '5.1.6',
            ),
        ),
        'cakephp/i18n' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '5.1.6',
            ),
        ),
        'cakephp/log' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '5.1.6',
            ),
        ),
        'cakephp/migrations' => array(
            'pretty_version' => '4.6.4',
            'version' => '4.6.4.0',
            'reference' => 'a735a6f39e8452727b6281834529d40bc6efe741',
            'type' => 'cakephp-plugin',
            'install_path' => __DIR__ . '/../cakephp/migrations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cakephp/orm' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '5.1.6',
            ),
        ),
        'cakephp/plugin-installer' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => '5420701fd47d82fe81805ebee34fbbcef34c52ba',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../cakephp/plugin-installer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cakephp/twig-view' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'b11df8e8734ae556d98b143192377dbc6a6f5360',
            'type' => 'cakephp-plugin',
            'install_path' => __DIR__ . '/../cakephp/twig-view',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'cakephp/utility' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '5.1.6',
            ),
        ),
        'cakephp/validation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '5.1.6',
            ),
        ),
        'composer/ca-bundle' => array(
            'pretty_version' => '1.5.7',
            'version' => '1.5.7.0',
            'reference' => 'd665d22c417056996c59019579f1967dfe5c1e82',
            'type' => 'library',
            'install_path' => __DIR__ . '/./ca-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/class-map-generator' => array(
            'pretty_version' => '1.6.1',
            'version' => '1.6.1.0',
            'reference' => '134b705ddb0025d397d8318a75825fe3c9d1da34',
            'type' => 'library',
            'install_path' => __DIR__ . '/./class-map-generator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/composer' => array(
            'pretty_version' => '2.8.9',
            'version' => '2.8.9.0',
            'reference' => 'b4e6bff2db7ce756ddb77ecee958a0f41f42bd9d',
            'type' => 'library',
            'install_path' => __DIR__ . '/./composer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/metadata-minifier' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'c549d23829536f0d0e984aaabbf02af91f443207',
            'type' => 'library',
            'install_path' => __DIR__ . '/./metadata-minifier',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/pcre' => array(
            'pretty_version' => '3.3.2',
            'version' => '3.3.2.0',
            'reference' => 'b2bed4734f0cc156ee1fe9c0da2550420d99a21e',
            'type' => 'library',
            'install_path' => __DIR__ . '/./pcre',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.4.3',
            'version' => '3.4.3.0',
            'reference' => '4313d26ada5e0c4edfbd1dc481a92ff7bff91f12',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/spdx-licenses' => array(
            'pretty_version' => '1.5.9',
            'version' => '1.5.9.0',
            'reference' => 'edf364cefe8c43501e21e88110aac10b284c3c9f',
            'type' => 'library',
            'install_path' => __DIR__ . '/./spdx-licenses',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/xdebug-handler' => array(
            'pretty_version' => '3.0.5',
            'version' => '3.0.5.0',
            'reference' => '6c1925561632e83d60a44492e0b344cf48ab85ef',
            'type' => 'library',
            'install_path' => __DIR__ . '/./xdebug-handler',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'dealerdirect/phpcodesniffer-composer-installer' => array(
            'pretty_version' => 'v1.1.0',
            'version' => '1.1.0.0',
            'reference' => '18a95476797ed480b3f2598984bc6f7e1eecc9a8',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../dealerdirect/phpcodesniffer-composer-installer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'doctrine/sql-formatter' => array(
            'pretty_version' => '1.5.2',
            'version' => '1.5.2.0',
            'reference' => 'd6d00aba6fd2957fe5216fe2b7673e9985db20c8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/sql-formatter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'jasny/twig-extensions' => array(
            'pretty_version' => 'v1.3.1',
            'version' => '1.3.1.0',
            'reference' => '8a5ca5f49317bf421a519556ad2e876820d41e01',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jasny/twig-extensions',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'josegonzalez/dotenv' => array(
            'pretty_version' => '4.0.0',
            'version' => '4.0.0.0',
            'reference' => 'e97dbd3db53508dcd536e73ec787a7f11458d41d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../josegonzalez/dotenv',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'justinrainbow/json-schema' => array(
            'pretty_version' => '6.4.2',
            'version' => '6.4.2.0',
            'reference' => 'ce1fd2d47799bb60668643bc6220f6278a4c1d02',
            'type' => 'library',
            'install_path' => __DIR__ . '/../justinrainbow/json-schema',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'laminas/laminas-diactoros' => array(
            'pretty_version' => '3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'b068eac123f21c0e592de41deeb7403b88e0a89f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-diactoros',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-httphandlerrunner' => array(
            'pretty_version' => '2.12.0',
            'version' => '2.12.0.0',
            'reference' => 'b14da3519c650e9436e410cfedee6f860312eff9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-httphandlerrunner',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/container' => array(
            'pretty_version' => '4.2.5',
            'version' => '4.2.5.0',
            'reference' => 'd3cebb0ff4685ff61c749e54b27db49319e2ec00',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'm1/env' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '5c296e3e13450a207e12b343f3af1d7ab569f6f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../m1/env',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'marc-mabe/php-enum' => array(
            'pretty_version' => 'v4.7.1',
            'version' => '4.7.1.0',
            'reference' => '7159809e5cfa041dca28e61f7f7ae58063aae8ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../marc-mabe/php-enum',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'mobiledetect/mobiledetectlib' => array(
            'pretty_version' => '4.8.09',
            'version' => '4.8.09.0',
            'reference' => 'a06fe2e546a06bb8c2639d6823d5250b2efb3209',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mobiledetect/mobiledetectlib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.13.1',
            'version' => '1.13.1.0',
            'reference' => '1720ddd719e16cf0db4eb1c6eca108031636d46c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v5.5.0',
            'version' => '5.5.0.0',
            'reference' => 'ae59794362fe85e051a58ad36b289443f57be7a9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'orno/di' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '~2.0',
            ),
        ),
        'phar-io/manifest' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '54750ef60c58e43759730615a392c31c80e23176',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/manifest',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phar-io/version' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpstan/phpdoc-parser' => array(
            'pretty_version' => '2.1.0',
            'version' => '2.1.0.0',
            'reference' => '9b30d6fd026b2c132b3985ce6b23bec09ab3aa68',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpstan/phpdoc-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '11.0.10',
            'version' => '11.0.10.0',
            'reference' => '1a800a7446add2d79cc6b3c01c45381810367d76',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '5.1.0',
            'version' => '5.1.0.0',
            'reference' => '118cfaaa8bc5aef3287bf315b6060b1174754af6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-invoker' => array(
            'pretty_version' => '5.0.1',
            'version' => '5.0.1.0',
            'reference' => 'c1ca3814734c07492b3d4c5f794f4b0995333da2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-invoker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => '3e0404dc6b300e6bf56415467ebcb3fe4f33e964',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '7.0.1',
            'version' => '7.0.1.0',
            'reference' => '3b415def83fbcb41f991d9ebf16ae4ad8b7837b3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '11.5.24',
            'version' => '11.5.24.0',
            'reference' => '6b07ab1047155cf38f82dd691787a277782271dd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '^2.0',
                1 => '^1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '^1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '^1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '^1.1 || ^2.0',
            ),
        ),
        'psr/http-server-handler' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => '84c4fb66179be4caaf8e97bd239203245302e7d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-server-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-server-handler-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '^1.0',
            ),
        ),
        'psr/http-server-middleware' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'c1481f747daaa6a0782775cd6a8c26a1bf4a3829',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-server-middleware',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-server-middleware-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '^1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '^3.0',
                1 => '1.0|2.0|3.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '^3.0',
            ),
        ),
        'react/promise' => array(
            'pretty_version' => 'v3.2.0',
            'version' => '3.2.0.0',
            'reference' => '8a164643313c71354582dc850b42b33fa12a4b63',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/promise',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'robmorgan/phinx' => array(
            'pretty_version' => '0.16.9',
            'version' => '0.16.9.0',
            'reference' => '524ebdeb0e1838a845d752a3418726b38cd1e654',
            'type' => 'library',
            'install_path' => __DIR__ . '/../robmorgan/phinx',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/cli-parser' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => '15c5dd40dc4f38794d383bb95465193f5e0ae180',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/cli-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '54391c61e4af8078e5b276ab082b6d3c54c9ad64',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => '183a9b2632194febd219bb9246eee421dad8d45e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '6.3.1',
            'version' => '6.3.1.0',
            'reference' => '24b8fbc2c8e201bb1308e7b05148d6ab393b6959',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/complexity' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => 'ee41d384ab1906c68852636b6de493846e13e5a0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/complexity',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '6.0.2',
            'version' => '6.0.2.0',
            'reference' => 'b4ccd857127db5d41a5b676f24b51371d76d8544',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '7.2.1',
            'version' => '7.2.1.0',
            'reference' => 'a5c75038693ad2e8d4b6c15ba2403532647830c4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '6.3.0',
            'version' => '6.3.0.0',
            'reference' => '3473f61172093b2da7de1fb5782e1f24cc036dc3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '7.0.2',
            'version' => '7.0.2.0',
            'reference' => '3be331570a721f9a4b5917f4209773de17f747d7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/lines-of-code' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => 'd36ad0d782e5756913e42ad87cb2890f4ffe467a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/lines-of-code',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '6.0.1',
            'version' => '6.0.1.0',
            'reference' => 'f5b498e631a74204185071eb41f33f38d64608aa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-reflector' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => '6e1a43b411b2ad34146dee7524cb13a068bb35f9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-reflector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '6.0.2',
            'version' => '6.0.2.0',
            'reference' => '694d156164372abbd149a4b85ccda2e4670c0e16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/type' => array(
            'pretty_version' => '5.1.2',
            'version' => '5.1.2.0',
            'reference' => 'a8a7e30534b0eb0c77cd9d07e82de1a114389f5e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/type',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/version' => array(
            'pretty_version' => '5.0.2',
            'version' => '5.0.2.0',
            'reference' => 'c687e3387b99f5b03b6caa64c74b63e2936ff874',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'seld/jsonlint' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '1748aaf847fc731cfad7725aec413ee46f0cc3a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../seld/jsonlint',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'seld/phar-utils' => array(
            'pretty_version' => '1.2.1',
            'version' => '1.2.1.0',
            'reference' => 'ea2f4014f163c1be4c601b9b7bd6af81ba8d701c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../seld/phar-utils',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'seld/signal-handler' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => '04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98',
            'type' => 'library',
            'install_path' => __DIR__ . '/../seld/signal-handler',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'slevomat/coding-standard' => array(
            'pretty_version' => '8.19.1',
            'version' => '8.19.1.0',
            'reference' => '458d665acd49009efebd7e0cb385d71ae9ac3220',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../slevomat/coding-standard',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'squizlabs/php_codesniffer' => array(
            'pretty_version' => '3.13.2',
            'version' => '3.13.2.0',
            'reference' => '5b5e3821314f947dd040c70f7992a64eac89025c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../squizlabs/php_codesniffer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'staabm/side-effects-detector' => array(
            'pretty_version' => '1.0.5',
            'version' => '1.0.5.0',
            'reference' => 'd8334211a140ce329c13726d4a715adbddd0a163',
            'type' => 'library',
            'install_path' => __DIR__ . '/../staabm/side-effects-detector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/config' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'ba62ae565f1327c2f6366726312ed828c85853bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '66c1440edf6f339fd82ed6c7caa76cb006211b44',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'b8dce482de9d7c9fe2891155035a7248ab5c7fdb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'ec2344cf77a48253bbca6939aa3d2477773ea63d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php73' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '0f68c03565dcaaf25a890667542e8bd75fe7e5bb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php73',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '0cc9dd0f17f61d8131e7df6b84bd344899fe2608',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-php81' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php81',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '40c295f2deb408d5e9d2d32b8ba1dd61e36f05af',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'f021b05a130d35510bd6b25fe9053c2a8a15d5d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'f3570b8c61ca887a9e2938e85cb6458515d2b125',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'theseer/tokenizer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'reference' => '737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theseer/tokenizer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'twig/markdown-extra' => array(
            'pretty_version' => 'v3.21.0',
            'version' => '3.21.0.0',
            'reference' => 'f4616e1dd375209dacf6026f846e6b537d036ce4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twig/markdown-extra',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'twig/twig' => array(
            'pretty_version' => 'v3.21.1',
            'version' => '3.21.1.0',
            'reference' => '285123877d4dd97dd7c11842ac5fb7e86e60d81d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twig/twig',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
    ),
);
