#!/usr/bin/env php
<?php

if("cli" !== php_sapi_name()) {
    echo "<p>Run this PHP script from the command line to see CLI syntax highlighting and formatting.  It supports Unix pipes or command line argument style.</p>";
    echo "<pre><code>php bin/sql-formatter \"SELECT * FROM MyTable WHERE (id>5 AND \\`name\\` LIKE \\&quot;testing\\&quot;);\"</code></pre>";
    echo "<pre><code>echo \"SELECT * FROM MyTable WHERE (id>5 AND \\`name\\` LIKE \\&quot;testing\\&quot;);\" | php bin/sql-formatter</code></pre>";
    exit;
}

if(isset($argv[1])) {
    $sql = $argv[1];
}
else {
    $sql = stream_get_contents(fopen('php://stdin', 'r'));
}

$autoloadFiles = [
    __DIR__ . '/../vendor/autoload.php',
    __DIR__ . '/../../../autoload.php'
];

foreach ($autoloadFiles as $autoloadFile) {
    if (file_exists($autoloadFile)) {
        require_once $autoloadFile;
        break;
    }
}

echo (new \Doctrine\SqlFormatter\SqlFormatter())->format($sql);
