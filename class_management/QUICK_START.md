# 🚀 Hướng dẫn sử dụng nhanh

## 🌐 Truy cập hệ thống
**URL:** http://localhost:8765/users/login

## 🔑 Tài khoản đăng nhập

| <PERSON>ai trò | Email | Mật khẩu | Quyền hạn |
|---------|-------|----------|-----------|
| **Admin** | <EMAIL> | password | Toàn quyền |
| **Qu<PERSON>n sinh** | <EMAIL> | password | Quản lý (trừ thu chi) |
| **Gi<PERSON>o viên** | <EMAIL> | password | Check-in, hồ sơ |

## 📊 Dữ liệu mẫu có sẵn

### 👥 **5 học sinh:**
- **Lớp Toán 10A:** <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON>, Hoàng Văn <PERSON>
- **Lớ<PERSON> Văn 11B:** <PERSON><PERSON>, <PERSON><PERSON><PERSON>

### 📅 **Lị<PERSON> dạy hôm nay:**
- **07:00-09:00:** Lớp To<PERSON> 10A (Giáo viên 1)
- **13:00-15:00:** Lớp Văn 11B (Giáo viên 1)

### ⏰ **4 khung giờ:**
- Ca sáng 1: 07:00-09:00
- Ca sáng 2: 09:30-11:30  
- Ca trưa: 13:00-15:00
- Ca chiều: 15:30-17:30

## 🎯 Tính năng đã hoạt động

### 🔐 **Sau khi đăng nhập:**
1. **Dashboard** - Xem thống kê tổng quan
2. **Quản lý nhân viên** - CRUD nhân viên (Admin only)
3. **Đăng xuất** - An toàn

### 📱 **Giao diện:**
- Responsive design
- Mobile-friendly
- CSS tùy chỉnh đẹp mắt

## 🧪 Test các tính năng

### 1. **Đăng nhập Admin:**
- Vào http://localhost:8765/users/login
- Email: <EMAIL>
- Password: password
- ✅ Thấy dashboard với đầy đủ thống kê

### 2. **Quản lý nhân viên:**
- Từ dashboard → "Quản lý nhân viên"
- Xem danh sách 3 users
- Thêm/sửa/xóa nhân viên
- ✅ Chỉ admin mới thấy được

### 3. **Test phân quyền:**
- Đăng xuất → Đăng nhập bằng <EMAIL>
- ✅ Dashboard khác, ít thống kê hơn
- ✅ Không thấy "Quản lý nhân viên"

## 🚧 Đang phát triển

- ✅ Authentication & Authorization
- ✅ Dashboard & Reports  
- ✅ User Management
- 🔄 Student Management
- 🔄 Class Management
- 🔄 Schedule Management
- 🔄 Attendance System
- 🔄 Finance Management
- 🔄 Asset Management
- 🔄 Notification System

## 🆘 Troubleshooting

### Server không chạy:
```bash
cd class_management
bin/cake server
```

### Database lỗi:
```bash
bin/cake migrations migrate
```

### Lỗi quyền:
```bash
chmod -R 755 tmp/
chmod -R 755 logs/
```

---
**🎉 Hệ thống sẵn sàng sử dụng!**
