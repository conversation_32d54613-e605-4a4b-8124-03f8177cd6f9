@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  body {
    @apply font-sans text-gray-900 bg-gray-50;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold text-gray-900;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-danger {
    @apply bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .btn-outline {
    @apply border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500;
  }
  
  /* Card Components */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-gray-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-xl;
  }
  
  /* Form Components */
  .form-group {
    @apply mb-6;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }
  
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }
  
  .form-select {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }
  
  .form-error {
    @apply mt-1 text-sm text-danger-600;
  }
  
  /* Table Components */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }
  
  .table-body td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  .table-row:hover {
    @apply bg-gray-50;
  }
  
  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }
  
  .badge-success {
    @apply bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply bg-danger-100 text-danger-800;
  }
  
  .badge-gray {
    @apply bg-gray-100 text-gray-800;
  }
  
  /* Alert Components */
  .alert {
    @apply p-4 rounded-lg border;
  }
  
  .alert-success {
    @apply bg-success-50 border-success-200 text-success-800;
  }
  
  .alert-warning {
    @apply bg-warning-50 border-warning-200 text-warning-800;
  }
  
  .alert-danger {
    @apply bg-danger-50 border-danger-200 text-danger-800;
  }
  
  .alert-info {
    @apply bg-primary-50 border-primary-200 text-primary-800;
  }
  
  /* Navigation Components */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  }
  
  .nav-link-active {
    @apply bg-primary-100 text-primary-700;
  }
  
  .nav-link-inactive {
    @apply text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  }
  
  /* Sidebar */
  .sidebar {
    @apply bg-white border-r border-gray-200 w-64 fixed inset-y-0 left-0 z-50 transform transition-transform duration-300 ease-in-out;
  }
  
  .sidebar-header {
    @apply flex items-center justify-center h-16 border-b border-gray-200;
  }
  
  .sidebar-nav {
    @apply mt-6 px-3;
  }
  
  .sidebar-section {
    @apply mb-8;
  }
  
  .sidebar-section-title {
    @apply px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3;
  }
  
  /* Main Content */
  .main-content {
    @apply ml-64 min-h-screen bg-gray-50;
  }
  
  .content-header {
    @apply bg-white border-b border-gray-200 px-6 py-4;
  }
  
  .content-body {
    @apply p-6;
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .sidebar {
      @apply -translate-x-full;
    }
    
    .sidebar.open {
      @apply translate-x-0;
    }
    
    .main-content {
      @apply ml-0;
    }
  }
}
