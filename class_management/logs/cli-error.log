2025-06-26 08:41:36 warning: The connection is going to be closed but there is an active transaction.
2025-06-26 08:42:01 warning: The connection is going to be closed but there is an active transaction.
2025-06-26 08:42:26 warning: The connection is going to be closed but there is an active transaction.
2025-06-26 08:46:10 error: [ParseError] syntax error, unexpected token "class", expecting identifier in /Volumes/MOXC/Vscode/Class_lavareo/class_management/src/Model/Entity/Class.php on line 24
Stack Trace:
- ROOT/vendor/composer/ClassLoader.php:427
- [internal]:??
- CORE/src/ORM/Table.php:712
- ROOT/vendor/cakephp/bake/src/Command/ModelCommand.php:714
- ROOT/vendor/cakephp/bake/src/Command/ModelCommand.php:170
- ROOT/vendor/cakephp/bake/src/Command/ModelCommand.php:117
- ROOT/vendor/cakephp/bake/src/Command/ModelCommand.php:99
- CORE/src/Console/BaseCommand.php:202
- CORE/src/Console/CommandRunner.php:330
- CORE/src/Console/CommandRunner.php:168
- ROOT/bin/cake.php:10
- [main]:

2025-06-26 08:47:30 error: [Cake\ORM\Exception\MissingEntityException] Entity class `ClassRoom` could not be found. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/ORM/Table.php on line 740
Exception Attributes: array (
  0 => 'ClassRoom',
)
Stack Trace:
- APP/Model/Table/ClassesTable.php:49
- CORE/src/ORM/Table.php:328
- CORE/src/ORM/Locator/TableLocator.php:319
- CORE/src/ORM/Locator/TableLocator.php:272
- CORE/src/Datasource/Locator/AbstractLocator.php:68
- CORE/src/ORM/Locator/TableLocator.php:212
- ROOT/vendor/cakephp/bake/src/Command/ModelCommand.php:366
- ROOT/vendor/cakephp/bake/src/Command/ModelCommand.php:245
- ROOT/vendor/cakephp/bake/src/Command/ModelCommand.php:164
- ROOT/vendor/cakephp/bake/src/Command/ModelCommand.php:117
- ROOT/vendor/cakephp/bake/src/Command/ModelCommand.php:99
- CORE/src/Console/BaseCommand.php:202
- CORE/src/Console/CommandRunner.php:330
- CORE/src/Console/CommandRunner.php:168
- ROOT/bin/cake.php:10
- [main]:

