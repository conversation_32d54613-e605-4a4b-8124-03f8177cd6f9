2025-06-26 08:54:39 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/users/login` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/users/login',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /users/login
2025-06-26 08:55:55 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/9447a8e9-ba8c-4703-a131-d19697c53946` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/9447a8e9-ba8c-4703-a131-d19697c53946',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/9447a8e9-ba8c-4703-a131-d19697c53946
Referer URL: http://localhost:8765/users/login
2025-06-26 08:56:09 error: [Error] Call to undefined method App\Controller\DashboardController::loadModel() in /Volumes/MOXC/Vscode/Class_lavareo/class_management/src/Controller/DashboardController.php on line 28
Stack Trace:
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /
Referer URL: http://localhost:8765/users/login
2025-06-26 08:56:09 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/2e2ae9c7-d431-4ff2-a100-08c0a9aaf064` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/2e2ae9c7-d431-4ff2-a100-08c0a9aaf064',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/2e2ae9c7-d431-4ff2-a100-08c0a9aaf064
Referer URL: http://localhost:8765/
2025-06-26 08:57:17 error: [Error] Call to undefined method Authentication\Identity::isAdmin() in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/IdentityDecorator.php on line 114
Stack Trace:
- APP/Controller/DashboardController.php:39
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /
Referer URL: http://localhost:8765/users/login
2025-06-26 08:57:17 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/aaf05dcb-6c06-46c3-8464-98dcdb63b327` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/aaf05dcb-6c06-46c3-8464-98dcdb63b327',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/aaf05dcb-6c06-46c3-8464-98dcdb63b327
Referer URL: http://localhost:8765/
2025-06-26 08:57:20 error: [Error] Call to undefined method Authentication\Identity::isAdmin() in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/IdentityDecorator.php on line 114
Stack Trace:
- APP/Controller/DashboardController.php:39
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /
2025-06-26 08:57:21 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/2032ecdb-344f-4c0b-987d-675056dc941e` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/2032ecdb-344f-4c0b-987d-675056dc941e',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/2032ecdb-344f-4c0b-987d-675056dc941e
Referer URL: http://localhost:8765/
2025-06-26 08:57:22 error: [Error] Call to undefined method Authentication\Identity::isAdmin() in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/IdentityDecorator.php on line 114
Stack Trace:
- APP/Controller/DashboardController.php:39
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /
Referer URL: http://localhost:8765/users/login
2025-06-26 08:57:22 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/712f9146-731c-4f7c-85d6-4ce3dfe3a0ad` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/712f9146-731c-4f7c-85d6-4ce3dfe3a0ad',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/712f9146-731c-4f7c-85d6-4ce3dfe3a0ad
Referer URL: http://localhost:8765/
2025-06-26 08:57:23 error: [Error] Call to undefined method Authentication\Identity::isAdmin() in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/IdentityDecorator.php on line 114
Stack Trace:
- APP/Controller/DashboardController.php:39
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /
Referer URL: http://localhost:8765/users/login
2025-06-26 08:57:23 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/77c9f0af-e2e1-4ff1-9b5c-b8eed33e98a3` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/77c9f0af-e2e1-4ff1-9b5c-b8eed33e98a3',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/77c9f0af-e2e1-4ff1-9b5c-b8eed33e98a3
Referer URL: http://localhost:8765/
2025-06-26 08:57:27 error: [Error] Call to undefined method Authentication\Identity::isAdmin() in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/IdentityDecorator.php on line 114
Stack Trace:
- APP/Controller/DashboardController.php:39
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /
2025-06-26 08:57:27 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/83dc8ad5-ad1a-450c-8c3c-d9563c6c8b6a` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/83dc8ad5-ad1a-450c-8c3c-d9563c6c8b6a',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/83dc8ad5-ad1a-450c-8c3c-d9563c6c8b6a
Referer URL: http://localhost:8765/
2025-06-26 08:57:28 error: [Error] Call to undefined method Authentication\Identity::isAdmin() in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/IdentityDecorator.php on line 114
Stack Trace:
- APP/Controller/DashboardController.php:39
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /
Referer URL: http://localhost:8765/users/login
2025-06-26 08:57:28 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/cbf1447d-f92c-41cd-a603-3b83ff78df3e` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/cbf1447d-f92c-41cd-a603-3b83ff78df3e',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/cbf1447d-f92c-41cd-a603-3b83ff78df3e
Referer URL: http://localhost:8765/
2025-06-26 08:57:36 error: [Error] Call to undefined method Authentication\Identity::isAdmin() in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/IdentityDecorator.php on line 114
Stack Trace:
- APP/Controller/DashboardController.php:39
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /
Referer URL: http://localhost:8765/users/login
2025-06-26 08:57:36 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/fe78a67c-abfd-4f40-9a46-b330dfc308bb` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/fe78a67c-abfd-4f40-9a46-b330dfc308bb',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/fe78a67c-abfd-4f40-9a46-b330dfc308bb
Referer URL: http://localhost:8765/
2025-06-26 08:57:37 error: [Error] Call to undefined method Authentication\Identity::isAdmin() in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/IdentityDecorator.php on line 114
Stack Trace:
- APP/Controller/DashboardController.php:39
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /
Referer URL: http://localhost:8765/users/login
2025-06-26 08:57:37 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/6db39314-0f61-421b-88fa-5f3ffa5c2356` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/6db39314-0f61-421b-88fa-5f3ffa5c2356',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/6db39314-0f61-421b-88fa-5f3ffa5c2356
Referer URL: http://localhost:8765/
2025-06-26 08:57:37 error: [Error] Call to undefined method Authentication\Identity::isAdmin() in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/IdentityDecorator.php on line 114
Stack Trace:
- APP/Controller/DashboardController.php:39
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /
Referer URL: http://localhost:8765/users/login
2025-06-26 08:57:38 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/2e52a27b-d198-48c8-8abb-aeb365c380f9` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/2e52a27b-d198-48c8-8abb-aeb365c380f9',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/2e52a27b-d198-48c8-8abb-aeb365c380f9
Referer URL: http://localhost:8765/
2025-06-26 08:59:57 error: [Error] Call to undefined method Authentication\Identity::isAdmin() in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/IdentityDecorator.php on line 114
Stack Trace:
- APP/Controller/DashboardController.php:39
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /
Referer URL: http://localhost:8765/users/login
2025-06-26 08:59:57 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/033fa2b1-2180-4258-b9e8-0a845965619f` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/033fa2b1-2180-4258-b9e8-0a845965619f',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/033fa2b1-2180-4258-b9e8-0a845965619f
Referer URL: http://localhost:8765/
2025-06-26 08:59:58 error: [Error] Call to undefined method Authentication\Identity::isAdmin() in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/IdentityDecorator.php on line 114
Stack Trace:
- APP/Controller/DashboardController.php:39
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /
Referer URL: http://localhost:8765/users/login
2025-06-26 08:59:58 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/8dbd8be4-0e4a-4168-be9d-7900e782dbed` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/8dbd8be4-0e4a-4168-be9d-7900e782dbed',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/8dbd8be4-0e4a-4168-be9d-7900e782dbed
Referer URL: http://localhost:8765/
2025-06-26 08:59:58 error: [Error] Call to undefined method Authentication\Identity::isAdmin() in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/IdentityDecorator.php on line 114
Stack Trace:
- APP/Controller/DashboardController.php:39
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /
Referer URL: http://localhost:8765/users/login
2025-06-26 08:59:59 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/7bb57c37-70e7-4e3f-aea0-1033ffb1042f` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/7bb57c37-70e7-4e3f-aea0-1033ffb1042f',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/7bb57c37-70e7-4e3f-aea0-1033ffb1042f
Referer URL: http://localhost:8765/
2025-06-26 09:01:32 error: [Error] Call to undefined method Authentication\Identity::isAdmin() in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/IdentityDecorator.php on line 114
Stack Trace:
- APP/Controller/DashboardController.php:39
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /
2025-06-26 09:01:32 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/d1185dc2-8dff-4f89-9da1-3017e77c756a` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/d1185dc2-8dff-4f89-9da1-3017e77c756a',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/d1185dc2-8dff-4f89-9da1-3017e77c756a
Referer URL: http://localhost:8765/
2025-06-26 09:01:36 error: [Error] Call to undefined method Authentication\Identity::isAdmin() in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/IdentityDecorator.php on line 114
Stack Trace:
- APP/Controller/DashboardController.php:39
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /
Referer URL: http://localhost:8765/users/login
2025-06-26 09:01:36 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/9747f580-2240-49c2-afb2-6fe5bc53d0a3` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/9747f580-2240-49c2-afb2-6fe5bc53d0a3',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/9747f580-2240-49c2-afb2-6fe5bc53d0a3
Referer URL: http://localhost:8765/
2025-06-26 09:03:38 warning: Unknown: Failed to decode session object. Session has been destroyed
Request URL: /users/login
Trace:
Cake\Error\ErrorTrap->handleError() [internal], line ??
[main]

2025-06-26 09:04:05 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/6dd9966c-9c4f-4a62-8f74-15372355bd37` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/6dd9966c-9c4f-4a62-8f74-15372355bd37',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/6dd9966c-9c4f-4a62-8f74-15372355bd37
Referer URL: http://localhost:8765/users/login
2025-06-26 09:04:54 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/26baee91-4ae7-4e2b-9dd4-7e0aeff32020` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/26baee91-4ae7-4e2b-9dd4-7e0aeff32020',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/26baee91-4ae7-4e2b-9dd4-7e0aeff32020
Referer URL: http://localhost:8765/users/login
2025-06-26 09:05:14 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/87fa48bf-d087-4ac3-a3a6-75552c57435b` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/87fa48bf-d087-4ac3-a3a6-75552c57435b',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/87fa48bf-d087-4ac3-a3a6-75552c57435b
Referer URL: http://localhost:8765/users/login
2025-06-26 09:06:05 error: [Cake\Http\Exception\InvalidCsrfTokenException] Missing or incorrect CSRF cookie type. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/Http/Middleware/CsrfProtectionMiddleware.php on line 354
Stack Trace:
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:165
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:162
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /users/login
2025-06-26 09:06:15 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/e73f2f2e-9de2-4cf8-b0d8-b42762450923` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/e73f2f2e-9de2-4cf8-b0d8-b42762450923',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/e73f2f2e-9de2-4cf8-b0d8-b42762450923
Referer URL: http://localhost:8765/users/login
2025-06-26 09:06:30 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/e78e1f7e-8d64-493a-83e4-bb3aa3fdae2c` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/e78e1f7e-8d64-493a-83e4-bb3aa3fdae2c',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/e78e1f7e-8d64-493a-83e4-bb3aa3fdae2c
Referer URL: http://localhost:8765/
2025-06-26 09:06:35 error: [Cake\Http\Exception\MissingControllerException] Controller class `Students` could not be found. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/Controller/ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => 'Students',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE/src/Controller/ControllerFactory.php:77
- CORE/src/Http/BaseApplication.php:360
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /students
Referer URL: http://localhost:8765/
2025-06-26 09:06:35 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/46ce5d96-9525-4c3c-94ec-36b3ff2e7e85` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/46ce5d96-9525-4c3c-94ec-36b3ff2e7e85',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/46ce5d96-9525-4c3c-94ec-36b3ff2e7e85
Referer URL: http://localhost:8765/students
2025-06-26 09:06:39 error: [Authorization\Policy\Exception\MissingPolicyException] Policy for `App\Model\Table\UsersTable` has not been defined. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Policy/OrmResolver.php on line 159
Exception Attributes: array (
  0 => 'App\\Model\\Table\\UsersTable',
)
Stack Trace:
- ROOT/vendor/cakephp/authorization/src/Policy/OrmResolver.php:130
- ROOT/vendor/cakephp/authorization/src/Policy/OrmResolver.php:85
- ROOT/vendor/cakephp/authorization/src/AuthorizationService.php:88
- ROOT/vendor/cakephp/authorization/src/AuthorizationService.php:67
- ROOT/vendor/cakephp/authorization/src/IdentityDecorator.php:72
- ROOT/vendor/cakephp/authorization/src/Controller/Component/AuthorizationComponent.php:138
- ROOT/vendor/cakephp/authorization/src/Controller/Component/AuthorizationComponent.php:112
- ROOT/vendor/cakephp/authorization/src/Controller/Component/AuthorizationComponent.php:70
- APP/Controller/UsersController.php:32
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /users
Referer URL: http://localhost:8765/
2025-06-26 09:06:39 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/481af493-b4a2-4dbd-a1c4-0e4ec58ce72e` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/481af493-b4a2-4dbd-a1c4-0e4ec58ce72e',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/481af493-b4a2-4dbd-a1c4-0e4ec58ce72e
Referer URL: http://localhost:8765/users
2025-06-26 09:06:42 error: [Cake\Http\Exception\MissingControllerException] Controller class `Finances` could not be found. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/Controller/ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => 'Finances',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE/src/Controller/ControllerFactory.php:77
- CORE/src/Http/BaseApplication.php:360
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /finances
Referer URL: http://localhost:8765/
2025-06-26 09:06:42 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/ca275a1b-44cd-4798-8524-133e7b61c08f` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/ca275a1b-44cd-4798-8524-133e7b61c08f',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/ca275a1b-44cd-4798-8524-133e7b61c08f
Referer URL: http://localhost:8765/finances
2025-06-26 09:06:47 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/bea488bb-4069-468e-a35f-ed7a01b617ca` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/bea488bb-4069-468e-a35f-ed7a01b617ca',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/bea488bb-4069-468e-a35f-ed7a01b617ca
Referer URL: http://localhost:8765/
2025-06-26 09:06:52 error: [Cake\Http\Exception\MissingControllerException] Controller class `Notifications` could not be found. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/Controller/ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => 'Notifications',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE/src/Controller/ControllerFactory.php:77
- CORE/src/Http/BaseApplication.php:360
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /notifications/add
Referer URL: http://localhost:8765/
2025-06-26 09:06:52 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/1148f584-4553-4320-81c2-59438d5cde9f` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/1148f584-4553-4320-81c2-59438d5cde9f',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/1148f584-4553-4320-81c2-59438d5cde9f
Referer URL: http://localhost:8765/notifications/add
2025-06-26 09:06:54 error: [Cake\Http\Exception\MissingControllerException] Controller class `Attendance` could not be found. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/Controller/ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => 'Attendance',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE/src/Controller/ControllerFactory.php:77
- CORE/src/Http/BaseApplication.php:360
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /attendance
Referer URL: http://localhost:8765/
2025-06-26 09:06:54 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/398776f9-f90a-46d9-bea0-e88165b91aec` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/398776f9-f90a-46d9-bea0-e88165b91aec',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/398776f9-f90a-46d9-bea0-e88165b91aec
Referer URL: http://localhost:8765/attendance
2025-06-26 09:06:56 error: [Cake\Http\Exception\MissingControllerException] Controller class `Schedules` could not be found. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/Controller/ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => 'Schedules',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE/src/Controller/ControllerFactory.php:77
- CORE/src/Http/BaseApplication.php:360
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /schedules
Referer URL: http://localhost:8765/
2025-06-26 09:06:56 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/ea30e7c4-a194-41d2-99df-dda84725b1a4` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/ea30e7c4-a194-41d2-99df-dda84725b1a4',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/ea30e7c4-a194-41d2-99df-dda84725b1a4
Referer URL: http://localhost:8765/schedules
2025-06-26 09:06:58 error: [Cake\Http\Exception\MissingControllerException] Controller class `Classes` could not be found. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/Controller/ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => 'Classes',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE/src/Controller/ControllerFactory.php:77
- CORE/src/Http/BaseApplication.php:360
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /classes
Referer URL: http://localhost:8765/
2025-06-26 09:06:58 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/84dea9e7-df86-4126-93d4-3a0cce41ba36` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/84dea9e7-df86-4126-93d4-3a0cce41ba36',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/84dea9e7-df86-4126-93d4-3a0cce41ba36
Referer URL: http://localhost:8765/classes
2025-06-26 09:07:15 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/9c58dbc1-cc37-4d7c-89cd-dcebe2db9ed3` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/9c58dbc1-cc37-4d7c-89cd-dcebe2db9ed3',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/9c58dbc1-cc37-4d7c-89cd-dcebe2db9ed3
Referer URL: http://localhost:8765/
2025-06-26 09:07:17 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/be167069-95df-4703-b37d-72a4d61d09d2` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/be167069-95df-4703-b37d-72a4d61d09d2',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/be167069-95df-4703-b37d-72a4d61d09d2
Referer URL: http://localhost:8765/users/login
2025-06-26 09:07:29 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/705087ff-8d03-470e-89ed-036bfdb6948f` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/705087ff-8d03-470e-89ed-036bfdb6948f',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/705087ff-8d03-470e-89ed-036bfdb6948f
Referer URL: http://localhost:8765/
2025-06-26 09:07:49 error: [Authorization\Policy\Exception\MissingPolicyException] Policy for `App\Model\Table\UsersTable` has not been defined. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Policy/OrmResolver.php on line 159
Exception Attributes: array (
  0 => 'App\\Model\\Table\\UsersTable',
)
Stack Trace:
- ROOT/vendor/cakephp/authorization/src/Policy/OrmResolver.php:130
- ROOT/vendor/cakephp/authorization/src/Policy/OrmResolver.php:85
- ROOT/vendor/cakephp/authorization/src/AuthorizationService.php:88
- ROOT/vendor/cakephp/authorization/src/AuthorizationService.php:67
- ROOT/vendor/cakephp/authorization/src/IdentityDecorator.php:72
- ROOT/vendor/cakephp/authorization/src/Controller/Component/AuthorizationComponent.php:138
- ROOT/vendor/cakephp/authorization/src/Controller/Component/AuthorizationComponent.php:112
- ROOT/vendor/cakephp/authorization/src/Controller/Component/AuthorizationComponent.php:70
- APP/Controller/UsersController.php:32
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /users
Referer URL: http://localhost:8765/
2025-06-26 09:07:49 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/f1ed4287-88ec-456d-ab3b-5e0ffbd6233e` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/f1ed4287-88ec-456d-ab3b-5e0ffbd6233e',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/f1ed4287-88ec-456d-ab3b-5e0ffbd6233e
Referer URL: http://localhost:8765/users
2025-06-26 09:07:53 error: [Cake\Http\Exception\MissingControllerException] Controller class `Students` could not be found. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/Controller/ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => 'Students',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE/src/Controller/ControllerFactory.php:77
- CORE/src/Http/BaseApplication.php:360
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /students
Referer URL: http://localhost:8765/
2025-06-26 09:07:53 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/28996ebb-9431-453a-b820-b83f0a3dd665` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/28996ebb-9431-453a-b820-b83f0a3dd665',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/28996ebb-9431-453a-b820-b83f0a3dd665
Referer URL: http://localhost:8765/students
2025-06-26 09:08:07 error: [Authorization\Policy\Exception\MissingPolicyException] Policy for `App\Model\Table\UsersTable` has not been defined. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Policy/OrmResolver.php on line 159
Exception Attributes: array (
  0 => 'App\\Model\\Table\\UsersTable',
)
Stack Trace:
- ROOT/vendor/cakephp/authorization/src/Policy/OrmResolver.php:130
- ROOT/vendor/cakephp/authorization/src/Policy/OrmResolver.php:85
- ROOT/vendor/cakephp/authorization/src/AuthorizationService.php:88
- ROOT/vendor/cakephp/authorization/src/AuthorizationService.php:67
- ROOT/vendor/cakephp/authorization/src/IdentityDecorator.php:72
- ROOT/vendor/cakephp/authorization/src/Controller/Component/AuthorizationComponent.php:138
- ROOT/vendor/cakephp/authorization/src/Controller/Component/AuthorizationComponent.php:112
- ROOT/vendor/cakephp/authorization/src/Controller/Component/AuthorizationComponent.php:70
- APP/Controller/UsersController.php:32
- CORE/src/Controller/Controller.php:505
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /users
Referer URL: http://localhost:8765/
2025-06-26 09:08:07 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/83b7832e-d840-4346-b7ac-04acf8a0b755` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/83b7832e-d840-4346-b7ac-04acf8a0b755',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/83b7832e-d840-4346-b7ac-04acf8a0b755
Referer URL: http://localhost:8765/users
2025-06-26 09:11:30 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/0054bcb9-9f1c-4b1f-bba5-59c34454e847` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/0054bcb9-9f1c-4b1f-bba5-59c34454e847',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/0054bcb9-9f1c-4b1f-bba5-59c34454e847
Referer URL: http://localhost:8765/
2025-06-26 09:11:52 error: [Cake\View\Exception\MissingTemplateException] Template file `Students/index.php` could not be found.

The following paths were searched:

- `/Volumes/MOXC/Vscode/Class_lavareo/class_management/templates/Students/index.php`
- `/Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/templates/Students/index.php`
 in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/View/View.php on line 1377
Exception Attributes: array (
  'file' => 'Students/index.php',
  'paths' => 
  array (
    0 => '/Volumes/MOXC/Vscode/Class_lavareo/class_management/templates/',
    1 => '/Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/templates/',
  ),
)
Stack Trace:
- CORE/src/View/View.php:782
- CORE/src/Controller/Controller.php:712
- CORE/src/Controller/Controller.php:516
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /students
Referer URL: http://localhost:8765/
2025-06-26 09:11:52 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/b45e02e9-4998-4fc5-b3fe-3fec3f57064f` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/b45e02e9-4998-4fc5-b3fe-3fec3f57064f',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/b45e02e9-4998-4fc5-b3fe-3fec3f57064f
Referer URL: http://localhost:8765/students
2025-06-26 09:11:55 error: [Cake\Http\Exception\MissingControllerException] Controller class `Finances` could not be found. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/Controller/ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => 'Finances',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE/src/Controller/ControllerFactory.php:77
- CORE/src/Http/BaseApplication.php:360
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /finances
Referer URL: http://localhost:8765/
2025-06-26 09:11:55 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/412aa09f-d641-47cc-9ee3-7754949956e4` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/412aa09f-d641-47cc-9ee3-7754949956e4',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/412aa09f-d641-47cc-9ee3-7754949956e4
Referer URL: http://localhost:8765/finances
2025-06-26 09:12:23 error: [Cake\View\Exception\MissingTemplateException] Template file `Students/index.php` could not be found.

The following paths were searched:

- `/Volumes/MOXC/Vscode/Class_lavareo/class_management/templates/Students/index.php`
- `/Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/templates/Students/index.php`
 in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/View/View.php on line 1377
Exception Attributes: array (
  'file' => 'Students/index.php',
  'paths' => 
  array (
    0 => '/Volumes/MOXC/Vscode/Class_lavareo/class_management/templates/',
    1 => '/Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/templates/',
  ),
)
Stack Trace:
- CORE/src/View/View.php:782
- CORE/src/Controller/Controller.php:712
- CORE/src/Controller/Controller.php:516
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /students
Referer URL: http://localhost:8765/
2025-06-26 09:12:23 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/7dce11c9-548d-4917-8d37-8a47adb2897d` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/7dce11c9-548d-4917-8d37-8a47adb2897d',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/7dce11c9-548d-4917-8d37-8a47adb2897d
Referer URL: http://localhost:8765/students
2025-06-26 09:12:26 error: [Cake\Http\Exception\MissingControllerException] Controller class `.wellKnown` could not be found. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/Controller/ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => '.wellKnown',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE/src/Controller/ControllerFactory.php:320
- CORE/src/Controller/ControllerFactory.php:75
- CORE/src/Http/BaseApplication.php:360
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:73
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /.well-known/appspecific/com.chrome.devtools.json
2025-06-26 09:12:39 error: [Cake\Http\Exception\MissingControllerException] Controller class `Finances` could not be found. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/Controller/ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => 'Finances',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE/src/Controller/ControllerFactory.php:77
- CORE/src/Http/BaseApplication.php:360
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /finances
Referer URL: http://localhost:8765/
2025-06-26 09:12:39 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/8cf5fd84-3719-4825-b137-927af991c272` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/8cf5fd84-3719-4825-b137-927af991c272',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/8cf5fd84-3719-4825-b137-927af991c272
Referer URL: http://localhost:8765/finances
2025-06-26 09:12:54 error: [Cake\Http\Exception\MissingControllerException] Controller class `Assets` could not be found. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/Controller/ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => 'Assets',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE/src/Controller/ControllerFactory.php:77
- CORE/src/Http/BaseApplication.php:360
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /assets
Referer URL: http://localhost:8765/
2025-06-26 09:12:55 error: [Cake\Http\Exception\MissingControllerException] Controller class `Assets` could not be found. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/Controller/ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => 'Assets',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE/src/Controller/ControllerFactory.php:77
- CORE/src/Http/BaseApplication.php:360
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /assets
Referer URL: http://localhost:8765/
2025-06-26 09:12:56 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/266c2bfa-9b05-4d1f-a9c0-82dc0139e0fd` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/266c2bfa-9b05-4d1f-a9c0-82dc0139e0fd',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/266c2bfa-9b05-4d1f-a9c0-82dc0139e0fd
Referer URL: http://localhost:8765/assets
2025-06-26 09:13:09 error: [Cake\View\Exception\MissingTemplateException] Template file `Classes/index.php` could not be found.

The following paths were searched:

- `/Volumes/MOXC/Vscode/Class_lavareo/class_management/templates/Classes/index.php`
- `/Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/templates/Classes/index.php`
 in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/View/View.php on line 1377
Exception Attributes: array (
  'file' => 'Classes/index.php',
  'paths' => 
  array (
    0 => '/Volumes/MOXC/Vscode/Class_lavareo/class_management/templates/',
    1 => '/Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/templates/',
  ),
)
Stack Trace:
- CORE/src/View/View.php:782
- CORE/src/Controller/Controller.php:712
- CORE/src/Controller/Controller.php:516
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /classes
Referer URL: http://localhost:8765/
2025-06-26 09:13:09 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/e02c9ea8-efca-448e-b565-41f1ca595f25` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/e02c9ea8-efca-448e-b565-41f1ca595f25',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/e02c9ea8-efca-448e-b565-41f1ca595f25
Referer URL: http://localhost:8765/classes
2025-06-26 09:13:21 error: [Cake\View\Exception\MissingTemplateException] Template file `Schedules/index.php` could not be found.

The following paths were searched:

- `/Volumes/MOXC/Vscode/Class_lavareo/class_management/templates/Schedules/index.php`
- `/Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/templates/Schedules/index.php`
 in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/src/View/View.php on line 1377
Exception Attributes: array (
  'file' => 'Schedules/index.php',
  'paths' => 
  array (
    0 => '/Volumes/MOXC/Vscode/Class_lavareo/class_management/templates/',
    1 => '/Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/cakephp/templates/',
  ),
)
Stack Trace:
- CORE/src/View/View.php:782
- CORE/src/Controller/Controller.php:712
- CORE/src/Controller/Controller.php:516
- CORE/src/Controller/ControllerFactory.php:166
- CORE/src/Controller/ControllerFactory.php:141
- CORE/src/Http/BaseApplication.php:362
- CORE/src/Http/Runner.php:86
- CORE/src/Http/Middleware/CsrfProtectionMiddleware.php:169
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Middleware/BodyParserMiddleware.php:157
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php:136
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /schedules
Referer URL: http://localhost:8765/
2025-06-26 09:13:22 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/db82f09d-d407-45bc-ba5c-1205e87c7b05` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/db82f09d-d407-45bc-ba5c-1205e87c7b05',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/db82f09d-d407-45bc-ba5c-1205e87c7b05
Referer URL: http://localhost:8765/schedules
2025-06-26 09:16:14 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/9a3d61f5-d81a-4aa1-a3d7-0959955a891e` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/9a3d61f5-d81a-4aa1-a3d7-0959955a891e',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/9a3d61f5-d81a-4aa1-a3d7-0959955a891e
Referer URL: http://localhost:8765/
2025-06-26 09:17:50 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/89e04736-257d-4951-b46b-59ec79d09113` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/89e04736-257d-4951-b46b-59ec79d09113',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/89e04736-257d-4951-b46b-59ec79d09113
Referer URL: http://localhost:8765/
2025-06-26 09:17:53 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/********-95de-472f-9038-f19df9e9ceaa` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/********-95de-472f-9038-f19df9e9ceaa',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/********-95de-472f-9038-f19df9e9ceaa
Referer URL: http://localhost:8765/users
2025-06-26 09:17:58 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/d2d7b1fd-313e-421d-a8e1-e74f8bde839f` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/d2d7b1fd-313e-421d-a8e1-e74f8bde839f',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/d2d7b1fd-313e-421d-a8e1-e74f8bde839f
Referer URL: http://localhost:8765/users/add
2025-06-26 09:18:04 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/bce06b04-3046-474f-81ad-c283200b1b06` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/bce06b04-3046-474f-81ad-c283200b1b06',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/bce06b04-3046-474f-81ad-c283200b1b06
Referer URL: http://localhost:8765/
2025-06-26 09:18:07 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/finances` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/finances',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /finances
Referer URL: http://localhost:8765/
2025-06-26 09:18:07 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/41b9b7d8-c1ed-4d29-a285-be9215587093` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/41b9b7d8-c1ed-4d29-a285-be9215587093',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/41b9b7d8-c1ed-4d29-a285-be9215587093
Referer URL: http://localhost:8765/finances
2025-06-26 09:18:09 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/students` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/students',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /students
Referer URL: http://localhost:8765/
2025-06-26 09:18:10 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/b34999d6-0e2a-45a2-af75-5d776a9271fb` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/b34999d6-0e2a-45a2-af75-5d776a9271fb',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/b34999d6-0e2a-45a2-af75-5d776a9271fb
Referer URL: http://localhost:8765/students
2025-06-26 09:18:23 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/finances` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/finances',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /finances
Referer URL: http://localhost:8765/
2025-06-26 09:18:23 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/9a740af2-283b-48a1-ad9e-1270c34bf2ec` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/9a740af2-283b-48a1-ad9e-1270c34bf2ec',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/9a740af2-283b-48a1-ad9e-1270c34bf2ec
Referer URL: http://localhost:8765/finances
2025-06-26 09:18:24 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/students` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/students',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /students
Referer URL: http://localhost:8765/
2025-06-26 09:18:24 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/73ec59c9-9a1c-4f46-808e-f9e41f7d1509` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/73ec59c9-9a1c-4f46-808e-f9e41f7d1509',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/73ec59c9-9a1c-4f46-808e-f9e41f7d1509
Referer URL: http://localhost:8765/students
2025-06-26 09:18:35 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/classes` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/classes',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /classes
Referer URL: http://localhost:8765/
2025-06-26 09:18:36 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/5af93b32-9a5c-493f-88b5-7ab2bc40ab24` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/5af93b32-9a5c-493f-88b5-7ab2bc40ab24',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/5af93b32-9a5c-493f-88b5-7ab2bc40ab24
Referer URL: http://localhost:8765/classes
2025-06-26 09:22:15 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/81aa4eb3-80fc-4164-b2e9-899bc1035d6a` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/81aa4eb3-80fc-4164-b2e9-899bc1035d6a',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/81aa4eb3-80fc-4164-b2e9-899bc1035d6a
Referer URL: http://localhost:8765/
2025-06-26 09:22:23 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/dde31a32-5f57-4aa3-bfb8-8936fac2d64a` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/dde31a32-5f57-4aa3-bfb8-8936fac2d64a',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/dde31a32-5f57-4aa3-bfb8-8936fac2d64a
Referer URL: http://localhost:8765/students
2025-06-26 09:22:29 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/finances` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/finances',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /finances
Referer URL: http://localhost:8765/
2025-06-26 09:22:30 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/86a721ec-44fc-49e2-a2bb-71a9906cdde7` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/86a721ec-44fc-49e2-a2bb-71a9906cdde7',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/86a721ec-44fc-49e2-a2bb-71a9906cdde7
Referer URL: http://localhost:8765/finances
2025-06-26 09:22:31 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/11f75e03-f9bc-4ed2-a726-1aa344929879` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/11f75e03-f9bc-4ed2-a726-1aa344929879',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/11f75e03-f9bc-4ed2-a726-1aa344929879
Referer URL: http://localhost:8765/classes
2025-06-26 09:22:35 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/8693319c-4c47-4399-bfb5-162fe76c84fd` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/8693319c-4c47-4399-bfb5-162fe76c84fd',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/8693319c-4c47-4399-bfb5-162fe76c84fd
Referer URL: http://localhost:8765/
2025-06-26 09:22:37 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/10a09747-1568-43c7-8281-4af0878aae3e` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/10a09747-1568-43c7-8281-4af0878aae3e',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/10a09747-1568-43c7-8281-4af0878aae3e
Referer URL: http://localhost:8765/schedules
2025-06-26 09:22:39 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/schedules/add` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/schedules/add',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /schedules/add
Referer URL: http://localhost:8765/schedules
2025-06-26 09:22:40 error: [Authorization\Exception\AuthorizationRequiredException] The request to `/debug-kit/toolbar/7f30503f-81ea-4bad-b75d-0aa8a029e178` did not apply any authorization checks. in /Volumes/MOXC/Vscode/Class_lavareo/class_management/vendor/cakephp/authorization/src/Middleware/AuthorizationMiddleware.php on line 139
Exception Attributes: array (
  'url' => '/debug-kit/toolbar/7f30503f-81ea-4bad-b75d-0aa8a029e178',
)
Stack Trace:
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/authentication/src/Middleware/AuthenticationMiddleware.php:106
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/RoutingMiddleware.php:118
- CORE/src/Http/Runner.php:82
- CORE/src/Routing/Middleware/AssetMiddleware.php:69
- CORE/src/Http/Runner.php:82
- CORE/src/Error/Middleware/ErrorHandlerMiddleware.php:115
- CORE/src/Http/Runner.php:82
- ROOT/vendor/cakephp/debug_kit/src/Middleware/DebugKitMiddleware.php:60
- CORE/src/Http/Runner.php:82
- CORE/src/Http/Runner.php:60
- CORE/src/Http/Server.php:104
- ROOT/webroot/index.php:37
- [main]:

Request URL: /debug-kit/toolbar/7f30503f-81ea-4bad-b75d-0aa8a029e178
Referer URL: http://localhost:8765/schedules/add
