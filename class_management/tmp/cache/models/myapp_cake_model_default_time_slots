1750946266
O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:10:"time_slots";s:11:" * _columns";a:8:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:50;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"period";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"start_time";a:7:{s:4:"type";s:4:"time";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"end_time";a:7:{s:4:"type";s:4:"time";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:6:"active";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:7:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:8:"modified";a:7:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}}s:11:" * _typeMap";a:8:{s:2:"id";s:7:"integer";s:4:"name";s:6:"string";s:6:"period";s:6:"string";s:10:"start_time";s:4:"time";s:8:"end_time";s:4:"time";s:6:"status";s:6:"string";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}
