1750946266
O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:8:"requests";s:11:" * _columns";a:6:{s:2:"id";a:7:{s:4:"type";s:4:"uuid";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:8:"baseType";N;s:9:"precision";N;s:7:"comment";N;}s:3:"url";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:8:"baseType";N;s:9:"precision";N;s:7:"comment";N;s:7:"collate";N;}s:12:"content_type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:8:"baseType";N;s:9:"precision";N;s:7:"comment";N;s:7:"collate";N;}s:11:"status_code";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:8:"baseType";N;s:9:"precision";N;s:7:"comment";N;s:13:"autoIncrement";N;}s:6:"method";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:8:"baseType";N;s:9:"precision";N;s:7:"comment";N;s:7:"collate";N;}s:12:"requested_at";a:7:{s:4:"type";s:8:"datetime";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:8:"baseType";N;s:9:"precision";N;s:7:"comment";N;}}s:11:" * _typeMap";a:6:{s:2:"id";s:4:"uuid";s:3:"url";s:4:"text";s:12:"content_type";s:6:"string";s:11:"status_code";s:7:"integer";s:6:"method";s:6:"string";s:12:"requested_at";s:8:"datetime";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:0:{}s:13:" * _temporary";b:0;}
