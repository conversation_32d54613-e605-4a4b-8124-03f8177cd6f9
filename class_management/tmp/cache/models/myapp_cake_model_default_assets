1750929402
O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:6:"assets";s:11:" * _columns";a:13:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"description";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"category";a:8:{s:4:"type";s:6:"string";s:6:"length";i:50;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"quantity";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"1";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"unit_price";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:15;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:11:"total_value";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:15;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:13:"purchase_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"assigned_to";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:9:"available";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"created_by";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"created";a:7:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:8:"modified";a:7:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}}s:11:" * _typeMap";a:13:{s:2:"id";s:7:"integer";s:4:"name";s:6:"string";s:11:"description";s:4:"text";s:8:"category";s:6:"string";s:8:"quantity";s:7:"integer";s:10:"unit_price";s:7:"decimal";s:11:"total_value";s:7:"decimal";s:13:"purchase_date";s:4:"date";s:11:"assigned_to";s:7:"integer";s:6:"status";s:6:"string";s:10:"created_by";s:7:"integer";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";}s:11:" * _indexes";a:4:{s:8:"category";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"category";}s:6:"length";a:0:{}}s:11:"assigned_to";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"assigned_to";}s:6:"length";a:0:{}}s:10:"created_by";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"created_by";}s:6:"length";a:0:{}}s:6:"status";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:6:"status";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:13:"assets_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"assigned_to";}s:10:"references";a:2:{i:0;s:5:"users";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"setNull";s:6:"length";a:0:{}}s:13:"assets_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"created_by";}s:10:"references";a:2:{i:0;s:5:"users";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}
