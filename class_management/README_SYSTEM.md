# 🎓 <PERSON><PERSON> thống quản lý lớp học

Hệ thống quản lý lớp học được xây dựng bằng CakePHP 5.1.6 và MySQL, hỗ trợ quản lý toàn diện các hoạt động giảng dạy và học tập.

## 🚀 Tính năng chính

### ✅ **Đã hoàn thành:**

#### 🔐 **Hệ thống xác thực và phân quyền**
- Đăng nhập/đăng xuất an toàn
- 3 cấp độ quyền hạn: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> sinh, Gi<PERSON><PERSON> viên
- Mã hóa mật khẩu bảo mật

#### 👥 **Quản lý nhân viên (Admin only)**
- CRUD đầy đủ cho nhân viên
- Phân quyền theo vai trò
- Giao diện thân thiện

#### 📊 **Dashboard thông minh**
- Th<PERSON><PERSON> kê theo quyền hạn
- Lịch dạy hôm nay
- <PERSON>áo cáo tổng quan

#### 🗄️ **C<PERSON> sở dữ liệu hoàn chỉnh**
- 9 bảng chính với relationships
- Dữ liệu mẫu sẵn sàng
- Foreign keys và indexes

## 🔑 **Thông tin đăng nhập**

| Vai trò | Email | Mật khẩu |
|---------|-------|----------|
| **Admin** | <EMAIL> | password |
| **Quản sinh** | <EMAIL> | password |
| **Giáo viên** | <EMAIL> | password |

## 🌐 **Truy cập hệ thống**

**URL:** http://localhost:8765/users/login

## 📋 **Cấu trúc dữ liệu**

### 👤 **Users (Nhân viên)**
- Admin: Toàn quyền quản lý
- Manager (Quản sinh): Quản lý trừ thu chi và thêm học sinh
- Teacher (Giáo viên): Check-in và quản lý hồ sơ cá nhân

### 🎓 **Students (Học sinh)**
- Mã học sinh tự động
- Thông tin cá nhân và phụ huynh
- Link Google Drive hồ sơ

### 🏫 **Classes (Lớp học)**
- Quản lý danh sách lớp
- Phân công giáo viên
- Giới hạn số học sinh

### ⏰ **Time Slots (Khung giờ)**
- Ca sáng, trưa, chiều
- Thời gian linh hoạt

### 📅 **Schedules (Lịch dạy)**
- Lên lịch theo lớp và khung giờ
- Tránh trùng lặp
- Theo dõi trạng thái

### ✅ **Attendance (Điểm danh)**
- Điểm danh theo lịch
- Trạng thái: Có mặt/Vắng/Muộn
- Ghi chú chi tiết

### 💰 **Finances (Thu chi)**
- Quản lý thu/chi
- Theo dõi học sinh
- Báo cáo tài chính

### 🏢 **Assets (Tài sản)**
- Quản lý tài sản
- Phân phối và theo dõi
- Trạng thái sử dụng

### 📢 **Notifications (Thông báo)**
- Gửi thông báo lịch học
- Phân loại đối tượng
- Theo dõi đã đọc

## 🛠️ **Cài đặt và chạy**

```bash
# 1. Clone project
cd /path/to/your/workspace

# 2. Cài đặt dependencies
composer install

# 3. Cấu hình database
# Chỉnh sửa config/app_local.php với thông tin MySQL

# 4. Chạy migrations
bin/cake migrations migrate

# 5. Khởi động server
bin/cake server

# 6. Truy cập: http://localhost:8765
```

## 📈 **Roadmap phát triển**

### 🔄 **Đang phát triển:**
- Module quản lý học sinh
- Module quản lý lớp học
- Module lịch dạy và khung giờ
- Module điểm danh
- Module thu chi và tài sản
- Module thông báo

### 🎯 **Tính năng nâng cao:**
- Báo cáo chi tiết
- Export/Import dữ liệu
- API REST
- Mobile app
- Tích hợp email/SMS

## 🔧 **Công nghệ sử dụng**

- **Backend:** CakePHP 5.1.6
- **Database:** MySQL 8.0+
- **Frontend:** HTML5, CSS3, JavaScript
- **Authentication:** CakePHP Authentication Plugin
- **Authorization:** CakePHP Authorization Plugin

## 📞 **Hỗ trợ**

Hệ thống được thiết kế để dễ dàng mở rộng và tùy chỉnh theo nhu cầu cụ thể của từng trường học.

---
*Phát triển bởi AI Assistant - CakePHP Class Management System*
