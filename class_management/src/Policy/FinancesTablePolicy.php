<?php
declare(strict_types=1);

namespace App\Policy;

use App\Model\Table\FinancesTable;
use Authorization\IdentityInterface;

/**
 * FinancesTable policy
 */
class FinancesTablePolicy extends ApplicationPolicy
{
    /**
     * Check if $user can index FinancesTable
     */
    public function canIndex(?IdentityInterface $user, $resource): bool
    {
        // All authenticated users can view list
        return $user !== null;
    }

    /**
     * Check if $user can add to FinancesTable
     */
    public function canAdd(?IdentityInterface $user, $resource): bool
    {
        // Admin and managers can add
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can view FinancesTable
     */
    public function canView(?IdentityInterface $user, $resource): bool
    {
        // All authenticated users can view
        return $user !== null;
    }

    /**
     * Check if $user can edit FinancesTable
     */
    public function canEdit(?IdentityInterface $user, $resource): bool
    {
        // Admin and managers can edit
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can delete from FinancesTable
     */
    public function canDelete(?IdentityInterface $user, $resource): bool
    {
        // Only admin can delete
        return $user && $user->role === 'admin';
    }
}