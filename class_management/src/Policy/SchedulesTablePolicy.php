<?php
declare(strict_types=1);

namespace App\Policy;

use App\Model\Table\SchedulesTable;
use Authorization\IdentityInterface;

/**
 * SchedulesTable policy
 */
class SchedulesTablePolicy extends ApplicationPolicy
{
    /**
     * Check if $user can index SchedulesTable
     */
    public function canIndex(?IdentityInterface $user, $resource): bool
    {
        // All authenticated users can view list
        return $user !== null;
    }

    /**
     * Check if $user can add to SchedulesTable
     */
    public function canAdd(?IdentityInterface $user, $resource): bool
    {
        // Admin and managers can add
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can view SchedulesTable
     */
    public function canView(?IdentityInterface $user, $resource): bool
    {
        // All authenticated users can view
        return $user !== null;
    }

    /**
     * Check if $user can edit SchedulesTable
     */
    public function canEdit(?IdentityInterface $user, $resource): bool
    {
        // Admin and managers can edit
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can delete from SchedulesTable
     */
    public function canDelete(?IdentityInterface $user, $resource): bool
    {
        // Only admin can delete
        return $user && $user->role === 'admin';
    }
}