<?php
declare(strict_types=1);

namespace App\Policy;

use App\Model\Table\UsersTable;
use Authorization\IdentityInterface;

/**
 * UsersTable policy
 */
class UsersTablePolicy extends ApplicationPolicy
{
    /**
     * Check if $user can index UsersTable
     *
     * @param \Authorization\IdentityInterface|null $user The user.
     * @param mixed $resource The resource being accessed.
     * @return bool
     */
    public function canIndex(?IdentityInterface $user, $resource): bool
    {
        // Only admin and managers can view user list
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can add to UsersTable
     *
     * @param \Authorization\IdentityInterface|null $user The user.
     * @param mixed $resource The resource being accessed.
     * @return bool
     */
    public function canAdd(?IdentityInterface $user, $resource): bool
    {
        // Only admin can add users
        return $user && $user->role === 'admin';
    }

    /**
     * Check if $user can view UsersTable
     *
     * @param \Authorization\IdentityInterface|null $user The user.
     * @param mixed $resource The resource being accessed.
     * @return bool
     */
    public function canView(?IdentityInterface $user, $resource): bool
    {
        // Admin and managers can view user data
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can edit UsersTable
     *
     * @param \Authorization\IdentityInterface|null $user The user.
     * @param mixed $resource The resource being accessed.
     * @return bool
     */
    public function canEdit(?IdentityInterface $user, $resource): bool
    {
        // Only admin can edit users
        return $user && $user->role === 'admin';
    }

    /**
     * Check if $user can delete from UsersTable
     *
     * @param \Authorization\IdentityInterface|null $user The user.
     * @param mixed $resource The resource being accessed.
     * @return bool
     */
    public function canDelete(?IdentityInterface $user, $resource): bool
    {
        // Only admin can delete users
        return $user && $user->role === 'admin';
    }
}
