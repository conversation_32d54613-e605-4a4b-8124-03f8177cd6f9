<?php
declare(strict_types=1);

namespace App\Policy;

use App\Model\Table\TimeSlotsTable;
use Authorization\IdentityInterface;

/**
 * TimeSlotsTable policy
 */
class TimeSlotsTablePolicy extends ApplicationPolicy
{
    /**
     * Check if $user can index TimeSlotsTable
     */
    public function canIndex(?IdentityInterface $user, $resource): bool
    {
        // All authenticated users can view list
        return $user !== null;
    }

    /**
     * Check if $user can add to TimeSlotsTable
     */
    public function canAdd(?IdentityInterface $user, $resource): bool
    {
        // Admin and managers can add
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can view TimeSlotsTable
     */
    public function canView(?IdentityInterface $user, $resource): bool
    {
        // All authenticated users can view
        return $user !== null;
    }

    /**
     * Check if $user can edit TimeSlotsTable
     */
    public function canEdit(?IdentityInterface $user, $resource): bool
    {
        // Admin and managers can edit
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can delete from TimeSlotsTable
     */
    public function canDelete(?IdentityInterface $user, $resource): bool
    {
        // Only admin can delete
        return $user && $user->role === 'admin';
    }
}