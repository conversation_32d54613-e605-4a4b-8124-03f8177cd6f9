<?php
declare(strict_types=1);

namespace App\Policy;

use App\Model\Table\ClassesTable;
use Authorization\IdentityInterface;

/**
 * ClassesTable policy
 */
class ClassesTablePolicy extends ApplicationPolicy
{
    /**
     * Check if $user can index ClassesTable
     */
    public function canIndex(?IdentityInterface $user, $resource): bool
    {
        // All authenticated users can view class list
        return $user !== null;
    }

    /**
     * Check if $user can add to ClassesTable
     */
    public function canAdd(?IdentityInterface $user, $resource): bool
    {
        // Admin and managers can add classes
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can view ClassesTable
     */
    public function canView(?IdentityInterface $user, $resource): bool
    {
        // All authenticated users can view class data
        return $user !== null;
    }

    /**
     * Check if $user can edit ClassesTable
     */
    public function canEdit(?IdentityInterface $user, $resource): bool
    {
        // Admin and managers can edit classes
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can delete from ClassesTable
     */
    public function canDelete(?IdentityInterface $user, $resource): bool
    {
        // Only admin can delete classes
        return $user && $user->role === 'admin';
    }
}
