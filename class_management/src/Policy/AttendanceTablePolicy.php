<?php
declare(strict_types=1);

namespace App\Policy;

use App\Model\Table\AttendanceTable;
use Authorization\IdentityInterface;

/**
 * AttendanceTable policy
 */
class AttendanceTablePolicy extends ApplicationPolicy
{
    /**
     * Check if $user can index AttendanceTable
     */
    public function canIndex(?IdentityInterface $user, $resource): bool
    {
        // All authenticated users can view list
        return $user !== null;
    }

    /**
     * Check if $user can add to AttendanceTable
     */
    public function canAdd(?IdentityInterface $user, $resource): bool
    {
        // Admin and managers can add
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can view AttendanceTable
     */
    public function canView(?IdentityInterface $user, $resource): bool
    {
        // All authenticated users can view
        return $user !== null;
    }

    /**
     * Check if $user can edit AttendanceTable
     */
    public function canEdit(?IdentityInterface $user, $resource): bool
    {
        // Admin and managers can edit
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can delete from AttendanceTable
     */
    public function canDelete(?IdentityInterface $user, $resource): bool
    {
        // Only admin can delete
        return $user && $user->role === 'admin';
    }
}