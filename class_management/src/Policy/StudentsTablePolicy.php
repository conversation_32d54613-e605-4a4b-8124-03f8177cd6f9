<?php
declare(strict_types=1);

namespace App\Policy;

use App\Model\Table\StudentsTable;
use Authorization\IdentityInterface;

/**
 * StudentsTable policy
 */
class StudentsTablePolicy extends ApplicationPolicy
{
    /**
     * Check if $user can index StudentsTable
     */
    public function canIndex(?IdentityInterface $user, $resource): bool
    {
        // Admin and managers can view student list
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can add to StudentsTable
     */
    public function canAdd(?IdentityInterface $user, $resource): bool
    {
        // Admin and managers can add students
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can view StudentsTable
     */
    public function canView(?IdentityInterface $user, $resource): bool
    {
        // Admin and managers can view student data
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can edit StudentsTable
     */
    public function canEdit(?IdentityInterface $user, $resource): bool
    {
        // Admin and managers can edit students
        return $user && in_array($user->role, ['admin', 'manager']);
    }

    /**
     * Check if $user can delete from StudentsTable
     */
    public function canDelete(?IdentityInterface $user, $resource): bool
    {
        // Only admin can delete students
        return $user && $user->role === 'admin';
    }
}
