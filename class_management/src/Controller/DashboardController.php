<?php
declare(strict_types=1);

namespace App\Controller;

/**
 * Dashboard Controller
 */
class DashboardController extends AppController
{
    /**
     * Index method - Main dashboard
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $user = $this->Authentication->getIdentity();
        
        // Load necessary tables
        $this->loadModel('Students');
        $this->loadModel('Classes');
        $this->loadModel('Schedules');
        $this->loadModel('Attendance');
        
        // Get today's date
        $today = date('Y-m-d');
        
        // Get statistics based on user role
        $stats = [];
        
        if ($user->isAdmin() || $user->isManager()) {
            // Admin and Manager can see all statistics
            $stats['total_students'] = $this->Students->find()->where(['status' => 'active'])->count();
            $stats['total_classes'] = $this->Classes->find()->where(['status' => 'active'])->count();
            $stats['today_schedules'] = $this->Schedules->find()
                ->where(['schedule_date' => $today, 'status' => 'scheduled'])
                ->count();
            
            // Get absent students today
            $stats['absent_today'] = $this->Attendance->find()
                ->contain(['Schedules'])
                ->where([
                    'Schedules.schedule_date' => $today,
                    'Attendance.status' => 'absent'
                ])
                ->count();
                
            // Get today's schedules with details
            $todaySchedules = $this->Schedules->find()
                ->contain(['Classes', 'TimeSlots', 'Teachers'])
                ->where(['schedule_date' => $today])
                ->orderBy(['TimeSlots.start_time' => 'ASC'])
                ->toArray();
                
            $this->set(compact('todaySchedules'));
        } else {
            // Teachers can only see their own statistics
            $stats['my_classes'] = $this->Classes->find()
                ->where(['teacher_id' => $user->id, 'status' => 'active'])
                ->count();
            $stats['my_schedules_today'] = $this->Schedules->find()
                ->where([
                    'teacher_id' => $user->id,
                    'schedule_date' => $today,
                    'status' => 'scheduled'
                ])
                ->count();
                
            // Get teacher's schedules today
            $todaySchedules = $this->Schedules->find()
                ->contain(['Classes', 'TimeSlots'])
                ->where([
                    'teacher_id' => $user->id,
                    'schedule_date' => $today
                ])
                ->orderBy(['TimeSlots.start_time' => 'ASC'])
                ->toArray();
                
            $this->set(compact('todaySchedules'));
        }
        
        $this->set(compact('stats', 'user'));
    }
}
