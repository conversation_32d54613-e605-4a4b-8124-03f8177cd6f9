<?php
declare(strict_types=1);

namespace App\Controller;

/**
 * Dashboard Controller
 */
class DashboardController extends AppController
{
    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);

        // Skip authorization for dashboard
        $this->Authorization->skipAuthorization(['index']);
    }
    /**
     * Index method - Main dashboard
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $user = $this->Authentication->getIdentity();

        // Load necessary tables
        $this->fetchTable('Students');
        $this->fetchTable('Classes');
        $this->fetchTable('Schedules');
        $this->fetchTable('Attendance');
        
        // Get today's date
        $today = date('Y-m-d');
        
        // Get statistics based on user role
        $stats = [];
        
        if ($user->isAdmin() || $user->isManager()) {
            // Admin and Manager can see all statistics
            $studentsTable = $this->fetchTable('Students');
            $classesTable = $this->fetchTable('Classes');
            $schedulesTable = $this->fetchTable('Schedules');
            $attendanceTable = $this->fetchTable('Attendance');

            $stats['total_students'] = $studentsTable->find()->where(['status' => 'active'])->count();
            $stats['total_classes'] = $classesTable->find()->where(['status' => 'active'])->count();
            $stats['today_schedules'] = $schedulesTable->find()
                ->where(['schedule_date' => $today, 'status' => 'scheduled'])
                ->count();

            // Get absent students today
            $stats['absent_today'] = $attendanceTable->find()
                ->contain(['Schedules'])
                ->where([
                    'Schedules.schedule_date' => $today,
                    'Attendance.status' => 'absent'
                ])
                ->count();

            // Get today's schedules with details
            $todaySchedules = $schedulesTable->find()
                ->contain(['Classes', 'TimeSlots', 'Teachers'])
                ->where(['schedule_date' => $today])
                ->orderBy(['TimeSlots.start_time' => 'ASC'])
                ->toArray();
                
            $this->set(compact('todaySchedules'));
        } else {
            // Teachers can only see their own statistics
            $classesTable = $this->fetchTable('Classes');
            $schedulesTable = $this->fetchTable('Schedules');

            $stats['my_classes'] = $classesTable->find()
                ->where(['teacher_id' => $user->id, 'status' => 'active'])
                ->count();
            $stats['my_schedules_today'] = $schedulesTable->find()
                ->where([
                    'teacher_id' => $user->id,
                    'schedule_date' => $today,
                    'status' => 'scheduled'
                ])
                ->count();

            // Get teacher's schedules today
            $todaySchedules = $schedulesTable->find()
                ->contain(['Classes', 'TimeSlots'])
                ->where([
                    'teacher_id' => $user->id,
                    'schedule_date' => $today
                ])
                ->orderBy(['TimeSlots.start_time' => 'ASC'])
                ->toArray();
                
            $this->set(compact('todaySchedules'));
        }
        
        $this->set(compact('stats', 'user'));
    }
}
