<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Notification Entity
 *
 * @property int $id
 * @property string $title
 * @property string $message
 * @property string $type
 * @property string $target_audience
 * @property int|null $target_user_id
 * @property int|null $schedule_id
 * @property bool $is_read
 * @property int $sent_by
 * @property \Cake\I18n\DateTime $sent_at
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\User $target_user
 * @property \App\Model\Entity\Schedule $schedule
 */
class Notification extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'title' => true,
        'message' => true,
        'type' => true,
        'target_audience' => true,
        'target_user_id' => true,
        'schedule_id' => true,
        'is_read' => true,
        'sent_by' => true,
        'sent_at' => true,
        'created' => true,
        'modified' => true,
        'target_user' => true,
        'schedule' => true,
    ];
}
