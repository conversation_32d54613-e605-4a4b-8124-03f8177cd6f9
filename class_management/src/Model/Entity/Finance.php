<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Finance Entity
 *
 * @property int $id
 * @property string $type
 * @property string $category
 * @property string $description
 * @property string $amount
 * @property \Cake\I18n\Date $transaction_date
 * @property int|null $student_id
 * @property int $created_by
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\Student $student
 */
class Finance extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'type' => true,
        'category' => true,
        'description' => true,
        'amount' => true,
        'transaction_date' => true,
        'student_id' => true,
        'created_by' => true,
        'created' => true,
        'modified' => true,
        'student' => true,
    ];
}
