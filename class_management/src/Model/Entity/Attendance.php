<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Attendance Entity
 *
 * @property int $id
 * @property int $schedule_id
 * @property int $student_id
 * @property string $status
 * @property \Cake\I18n\DateTime|null $check_in_time
 * @property string|null $notes
 * @property int $marked_by
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\Schedule $schedule
 * @property \App\Model\Entity\Student $student
 */
class Attendance extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'schedule_id' => true,
        'student_id' => true,
        'status' => true,
        'check_in_time' => true,
        'notes' => true,
        'marked_by' => true,
        'created' => true,
        'modified' => true,
        'schedule' => true,
        'student' => true,
    ];
}
