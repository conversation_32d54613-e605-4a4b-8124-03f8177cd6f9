<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Schedule Entity
 *
 * @property int $id
 * @property int $class_id
 * @property int $time_slot_id
 * @property int $teacher_id
 * @property \Cake\I18n\Date $schedule_date
 * @property string $status
 * @property string|null $notes
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\ClassRoom $class
 * @property \App\Model\Entity\TimeSlot $time_slot
 * @property \App\Model\Entity\User $teacher
 * @property \App\Model\Entity\Attendance[] $attendance
 * @property \App\Model\Entity\Notification[] $notifications
 */
class Schedule extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'class_id' => true,
        'time_slot_id' => true,
        'teacher_id' => true,
        'schedule_date' => true,
        'status' => true,
        'notes' => true,
        'created' => true,
        'modified' => true,
        'class' => true,
        'time_slot' => true,
        'teacher' => true,
        'attendance' => true,
        'notifications' => true,
    ];
}
