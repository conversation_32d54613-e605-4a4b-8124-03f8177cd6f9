<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Student Entity
 *
 * @property int $id
 * @property string $student_code
 * @property string $full_name
 * @property \Cake\I18n\Date $date_of_birth
 * @property string $parent_name
 * @property string $parent_phone
 * @property string|null $google_drive_link
 * @property int|null $class_id
 * @property string $status
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\Class $class
 * @property \App\Model\Entity\Attendance[] $attendance
 * @property \App\Model\Entity\Finance[] $finances
 */
class Student extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'student_code' => true,
        'full_name' => true,
        'date_of_birth' => true,
        'parent_name' => true,
        'parent_phone' => true,
        'google_drive_link' => true,
        'class_id' => true,
        'status' => true,
        'created' => true,
        'modified' => true,
        'class' => true,
        'attendance' => true,
        'finances' => true,
    ];
}
