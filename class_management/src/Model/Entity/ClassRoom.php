<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * ClassRoom Entity
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property int|null $teacher_id
 * @property int $max_students
 * @property string $status
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\User $teacher
 * @property \App\Model\Entity\Schedule[] $schedules
 * @property \App\Model\Entity\Student[] $students
 */
class ClassRoom extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'name' => true,
        'description' => true,
        'teacher_id' => true,
        'max_students' => true,
        'status' => true,
        'created' => true,
        'modified' => true,
        'teacher' => true,
        'schedules' => true,
        'students' => true,
    ];
}
