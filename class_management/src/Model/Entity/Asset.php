<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Asset Entity
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property string $category
 * @property int $quantity
 * @property string|null $unit_price
 * @property string|null $total_value
 * @property \Cake\I18n\Date|null $purchase_date
 * @property int|null $assigned_to
 * @property string $status
 * @property int $created_by
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 */
class Asset extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'name' => true,
        'description' => true,
        'category' => true,
        'quantity' => true,
        'unit_price' => true,
        'total_value' => true,
        'purchase_date' => true,
        'assigned_to' => true,
        'status' => true,
        'created_by' => true,
        'created' => true,
        'modified' => true,
    ];
}
