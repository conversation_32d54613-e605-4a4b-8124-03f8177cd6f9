<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * Classes Model
 *
 * @property \App\Model\Table\UsersTable&\Cake\ORM\Association\BelongsTo $Teachers
 * @property \App\Model\Table\SchedulesTable&\Cake\ORM\Association\HasMany $Schedules
 * @property \App\Model\Table\StudentsTable&\Cake\ORM\Association\HasMany $Students
 *
 * @method \App\Model\Entity\ClassRoom newEmptyEntity()
 * @method \App\Model\Entity\ClassRoom newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\ClassRoom> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\ClassRoom get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\ClassRoom findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\ClassRoom patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\ClassRoom> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\ClassRoom|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\ClassRoom saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\ClassRoom>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\ClassRoom>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\ClassRoom>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\ClassRoom> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\ClassRoom>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\ClassRoom>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\ClassRoom>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\ClassRoom> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class ClassesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('classes');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');
        $this->setEntityClass('ClassRoom');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Teachers', [
            'foreignKey' => 'teacher_id',
            'className' => 'Users',
        ]);
        $this->hasMany('Schedules', [
            'foreignKey' => 'class_id',
        ]);
        $this->hasMany('Students', [
            'foreignKey' => 'class_id',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('name')
            ->maxLength('name', 100)
            ->requirePresence('name', 'create')
            ->notEmptyString('name');

        $validator
            ->scalar('description')
            ->allowEmptyString('description');

        $validator
            ->integer('teacher_id')
            ->allowEmptyString('teacher_id');

        $validator
            ->integer('max_students')
            ->notEmptyString('max_students');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['teacher_id'], 'Teachers'), ['errorField' => 'teacher_id']);

        return $rules;
    }
}
