<?php
// Simple test script to verify login functionality
require_once 'vendor/autoload.php';

use Cake\Core\Configure;
use Cake\Datasource\ConnectionManager;
use Cake\Http\ServerRequest;
use Cake\Http\Response;
use Cake\Routing\Router;
use Cake\Core\Container;
use App\Application;

// Bootstrap the application
$app = new Application(__DIR__ . '/config');
$app->bootstrap();

// Test database connection
try {
    $connection = ConnectionManager::get('default');
    $result = $connection->execute('SELECT COUNT(*) as count FROM users')->fetch('assoc');
    echo "✅ Database connection successful. Found {$result['count']} users.\n";
    
    // Test user data
    $users = $connection->execute('SELECT email, role FROM users')->fetchAll('assoc');
    echo "📋 Available users:\n";
    foreach ($users as $user) {
        echo "   - {$user['email']} ({$user['role']})\n";
    }
    
    // Test students data
    $students = $connection->execute('SELECT COUNT(*) as count FROM students')->fetch('assoc');
    echo "👥 Found {$students['count']} students.\n";
    
    // Test classes data
    $classes = $connection->execute('SELECT COUNT(*) as count FROM classes')->fetch('assoc');
    echo "🏫 Found {$classes['count']} classes.\n";
    
    // Test schedules data
    $schedules = $connection->execute('SELECT COUNT(*) as count FROM schedules')->fetch('assoc');
    echo "📅 Found {$schedules['count']} schedules.\n";
    
    echo "\n🎉 All data looks good! You can now test the web interface.\n";
    echo "🌐 Visit: http://localhost:8765/users/login\n";
    echo "🔑 Login with: <EMAIL> / password\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
