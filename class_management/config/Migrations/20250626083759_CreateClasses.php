<?php
declare(strict_types=1);

use Migrations\BaseMigration;

class CreateClasses extends BaseMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/migrations/4/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('classes');
        $table->addColumn('name', 'string', ['limit' => 100, 'null' => false])
              ->addColumn('description', 'text', ['null' => true])
              ->addColumn('teacher_id', 'integer', ['null' => true])
              ->addColumn('max_students', 'integer', ['default' => 30])
              ->addColumn('status', 'enum', ['values' => ['active', 'inactive'], 'default' => 'active'])
              ->addColumn('created', 'datetime', ['null' => false])
              ->addColumn('modified', 'datetime', ['null' => false])
              ->addIndex(['teacher_id'])
              ->create();
    }
}
