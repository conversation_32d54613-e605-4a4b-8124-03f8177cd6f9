<?php
declare(strict_types=1);

use Migrations\BaseMigration;

class CreateAttendance extends BaseMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/migrations/4/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('attendance');
        $table->addColumn('schedule_id', 'integer', ['null' => false])
              ->addColumn('student_id', 'integer', ['null' => false])
              ->addColumn('status', 'enum', ['values' => ['present', 'absent', 'late'], 'default' => 'present'])
              ->addColumn('check_in_time', 'datetime', ['null' => true])
              ->addColumn('notes', 'text', ['null' => true])
              ->addColumn('marked_by', 'integer', ['null' => false])
              ->addColumn('created', 'datetime', ['null' => false])
              ->addColumn('modified', 'datetime', ['null' => false])
              ->addIndex(['schedule_id'])
              ->addIndex(['student_id'])
              ->addIndex(['marked_by'])
              ->addIndex(['schedule_id', 'student_id'], ['unique' => true])
              ->create();
    }
}
