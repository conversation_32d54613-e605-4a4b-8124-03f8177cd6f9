<?php
declare(strict_types=1);

use Migrations\BaseMigration;

class CreateStudents extends BaseMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/migrations/4/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('students');
        $table->addColumn('student_code', 'string', ['limit' => 20, 'null' => false])
              ->addColumn('full_name', 'string', ['limit' => 100, 'null' => false])
              ->addColumn('date_of_birth', 'date', ['null' => false])
              ->addColumn('parent_name', 'string', ['limit' => 100, 'null' => false])
              ->addColumn('parent_phone', 'string', ['limit' => 20, 'null' => false])
              ->addColumn('google_drive_link', 'text', ['null' => true])
              ->addColumn('class_id', 'integer', ['null' => true])
              ->addColumn('status', 'enum', ['values' => ['active', 'inactive'], 'default' => 'active'])
              ->addColumn('created', 'datetime', ['null' => false])
              ->addColumn('modified', 'datetime', ['null' => false])
              ->addIndex(['student_code'], ['unique' => true])
              ->addIndex(['class_id'])
              ->create();
    }
}
