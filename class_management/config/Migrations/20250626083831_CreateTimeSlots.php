<?php
declare(strict_types=1);

use Migrations\BaseMigration;

class CreateTimeSlots extends BaseMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/migrations/4/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('time_slots');
        $table->addColumn('name', 'string', ['limit' => 50, 'null' => false])
              ->addColumn('period', 'enum', ['values' => ['morning', 'afternoon', 'evening'], 'null' => false])
              ->addColumn('start_time', 'time', ['null' => false])
              ->addColumn('end_time', 'time', ['null' => false])
              ->addColumn('status', 'enum', ['values' => ['active', 'inactive'], 'default' => 'active'])
              ->addColumn('created', 'datetime', ['null' => false])
              ->addColumn('modified', 'datetime', ['null' => false])
              ->create();
    }
}
