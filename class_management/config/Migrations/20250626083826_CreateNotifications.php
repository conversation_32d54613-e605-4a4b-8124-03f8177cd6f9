<?php
declare(strict_types=1);

use Migrations\BaseMigration;

class CreateNotifications extends BaseMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/migrations/4/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('notifications');
        $table->addColumn('title', 'string', ['limit' => 200, 'null' => false])
              ->addColumn('message', 'text', ['null' => false])
              ->addColumn('type', 'enum', ['values' => ['schedule', 'general', 'urgent'], 'default' => 'general'])
              ->addColumn('target_audience', 'enum', ['values' => ['all', 'teachers', 'managers', 'specific'], 'default' => 'all'])
              ->addColumn('target_user_id', 'integer', ['null' => true])
              ->addColumn('schedule_id', 'integer', ['null' => true])
              ->addColumn('is_read', 'boolean', ['default' => false])
              ->addColumn('sent_by', 'integer', ['null' => false])
              ->addColumn('sent_at', 'datetime', ['null' => false])
              ->addColumn('created', 'datetime', ['null' => false])
              ->addColumn('modified', 'datetime', ['null' => false])
              ->addIndex(['type'])
              ->addIndex(['target_audience'])
              ->addIndex(['target_user_id'])
              ->addIndex(['schedule_id'])
              ->addIndex(['sent_by'])
              ->addIndex(['is_read'])
              ->create();
    }
}
