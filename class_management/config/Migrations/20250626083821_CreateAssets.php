<?php
declare(strict_types=1);

use Migrations\BaseMigration;

class CreateAssets extends BaseMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/migrations/4/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('assets');
        $table->addColumn('name', 'string', ['limit' => 100, 'null' => false])
              ->addColumn('description', 'text', ['null' => true])
              ->addColumn('category', 'string', ['limit' => 50, 'null' => false])
              ->addColumn('quantity', 'integer', ['default' => 1])
              ->addColumn('unit_price', 'decimal', ['precision' => 15, 'scale' => 2, 'null' => true])
              ->addColumn('total_value', 'decimal', ['precision' => 15, 'scale' => 2, 'null' => true])
              ->addColumn('purchase_date', 'date', ['null' => true])
              ->addColumn('assigned_to', 'integer', ['null' => true])
              ->addColumn('status', 'enum', ['values' => ['available', 'assigned', 'damaged', 'lost'], 'default' => 'available'])
              ->addColumn('created_by', 'integer', ['null' => false])
              ->addColumn('created', 'datetime', ['null' => false])
              ->addColumn('modified', 'datetime', ['null' => false])
              ->addIndex(['category'])
              ->addIndex(['assigned_to'])
              ->addIndex(['created_by'])
              ->addIndex(['status'])
              ->addForeignKey('assigned_to', 'users', 'id', ['delete' => 'SET_NULL', 'update' => 'CASCADE'])
              ->addForeignKey('created_by', 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
              ->create();
    }
}
