<?php
declare(strict_types=1);

use Migrations\BaseMigration;

class AddForeignKeys extends BaseMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/migrations/4/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        // Add foreign key for students table
        $this->table('students')
            ->addForeignKey('class_id', 'classes', 'id', ['delete' => 'SET_NULL', 'update' => 'CASCADE'])
            ->update();

        // Add foreign key for classes table
        $this->table('classes')
            ->addForeignKey('teacher_id', 'users', 'id', ['delete' => 'SET_NULL', 'update' => 'CASCADE'])
            ->update();

        // Add foreign keys for schedules table
        $this->table('schedules')
            ->addForeignKey('class_id', 'classes', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('time_slot_id', 'time_slots', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('teacher_id', 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->update();

        // Add foreign keys for attendance table
        $this->table('attendance')
            ->addForeignKey('schedule_id', 'schedules', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('student_id', 'students', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('marked_by', 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->update();

        // Add foreign keys for finances table
        $this->table('finances')
            ->addForeignKey('student_id', 'students', 'id', ['delete' => 'SET_NULL', 'update' => 'CASCADE'])
            ->addForeignKey('created_by', 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->update();

        // Add foreign keys for assets table
        $this->table('assets')
            ->addForeignKey('assigned_to', 'users', 'id', ['delete' => 'SET_NULL', 'update' => 'CASCADE'])
            ->addForeignKey('created_by', 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->update();

        // Add foreign keys for notifications table
        $this->table('notifications')
            ->addForeignKey('target_user_id', 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('schedule_id', 'schedules', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('sent_by', 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->update();
    }
}
