<?php
declare(strict_types=1);

use Migrations\BaseMigration;

class AddSampleStudents extends BaseMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/migrations/4/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        // Insert sample students
        $this->table('students')->insert([
            [
                'student_code' => 'HS001',
                'full_name' => 'Nguy<PERSON><PERSON>',
                'date_of_birth' => '2005-03-15',
                'parent_name' => '<PERSON><PERSON><PERSON><PERSON>',
                'parent_phone' => '0901234567',
                'google_drive_link' => 'https://drive.google.com/file/d/sample1',
                'class_id' => 1,
                'status' => 'active',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ],
            [
                'student_code' => 'HS002',
                'full_name' => 'Trần <PERSON>',
                'date_of_birth' => '2005-07-22',
                'parent_name' => 'Trầ<PERSON>',
                'parent_phone' => '0901234568',
                'google_drive_link' => 'https://drive.google.com/file/d/sample2',
                'class_id' => 1,
                'status' => 'active',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ],
            [
                'student_code' => 'HS003',
                'full_name' => 'Lê Minh Châu',
                'date_of_birth' => '2004-11-08',
                'parent_name' => 'Lê Văn Dũng',
                'parent_phone' => '0901234569',
                'google_drive_link' => 'https://drive.google.com/file/d/sample3',
                'class_id' => 2,
                'status' => 'active',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ],
            [
                'student_code' => 'HS004',
                'full_name' => 'Phạm Thị Dung',
                'date_of_birth' => '2004-12-25',
                'parent_name' => 'Phạm Văn Em',
                'parent_phone' => '0901234570',
                'google_drive_link' => 'https://drive.google.com/file/d/sample4',
                'class_id' => 2,
                'status' => 'active',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ],
            [
                'student_code' => 'HS005',
                'full_name' => 'Hoàng Văn Giang',
                'date_of_birth' => '2005-01-10',
                'parent_name' => 'Hoàng Văn Hải',
                'parent_phone' => '0901234571',
                'google_drive_link' => 'https://drive.google.com/file/d/sample5',
                'class_id' => 1,
                'status' => 'active',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ]
        ])->save();

        // Insert sample schedules for today
        $today = date('Y-m-d');
        $this->table('schedules')->insert([
            [
                'class_id' => 1,
                'time_slot_id' => 1,
                'teacher_id' => 3,
                'schedule_date' => $today,
                'status' => 'scheduled',
                'notes' => 'Lịch dạy Toán 10A ca sáng',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ],
            [
                'class_id' => 2,
                'time_slot_id' => 3,
                'teacher_id' => 3,
                'schedule_date' => $today,
                'status' => 'scheduled',
                'notes' => 'Lịch dạy Văn 11B ca trưa',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ]
        ])->save();
    }
}
