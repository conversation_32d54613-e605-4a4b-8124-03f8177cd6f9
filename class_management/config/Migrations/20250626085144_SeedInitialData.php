<?php
declare(strict_types=1);

use Migrations\BaseMigration;

class SeedInitialData extends BaseMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/migrations/4/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        // Insert admin user
        $this->table('users')->insert([
            [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
                'full_name' => 'Administrator',
                'phone' => '0123456789',
                'role' => 'admin',
                'status' => 'active',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ],
            [
                'username' => 'manager1',
                'email' => '<EMAIL>',
                'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
                'full_name' => 'Quản sinh 1',
                'phone' => '0123456790',
                'role' => 'manager',
                'status' => 'active',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ],
            [
                'username' => 'teacher1',
                'email' => '<EMAIL>',
                'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
                'full_name' => 'Giáo viên 1',
                'phone' => '0123456791',
                'role' => 'teacher',
                'status' => 'active',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ]
        ])->save();

        // Insert time slots
        $this->table('time_slots')->insert([
            [
                'name' => 'Ca sáng 1',
                'period' => 'morning',
                'start_time' => '07:00:00',
                'end_time' => '09:00:00',
                'status' => 'active',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Ca sáng 2',
                'period' => 'morning',
                'start_time' => '09:30:00',
                'end_time' => '11:30:00',
                'status' => 'active',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Ca trưa',
                'period' => 'afternoon',
                'start_time' => '13:00:00',
                'end_time' => '15:00:00',
                'status' => 'active',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Ca chiều',
                'period' => 'evening',
                'start_time' => '15:30:00',
                'end_time' => '17:30:00',
                'status' => 'active',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ]
        ])->save();

        // Insert sample classes
        $this->table('classes')->insert([
            [
                'name' => 'Lớp Toán 10A',
                'description' => 'Lớp học Toán lớp 10 ban A',
                'teacher_id' => 3, // teacher1
                'max_students' => 30,
                'status' => 'active',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Lớp Văn 11B',
                'description' => 'Lớp học Văn lớp 11 ban B',
                'teacher_id' => 3, // teacher1
                'max_students' => 25,
                'status' => 'active',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ]
        ])->save();
    }
}
