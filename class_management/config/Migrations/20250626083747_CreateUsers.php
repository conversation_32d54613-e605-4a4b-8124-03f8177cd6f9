<?php
declare(strict_types=1);

use Migrations\BaseMigration;

class CreateUsers extends BaseMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/migrations/4/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('users');
        $table->addColumn('username', 'string', ['limit' => 50, 'null' => false])
              ->addColumn('email', 'string', ['limit' => 100, 'null' => false])
              ->addColumn('password', 'string', ['limit' => 255, 'null' => false])
              ->addColumn('full_name', 'string', ['limit' => 100, 'null' => false])
              ->addColumn('phone', 'string', ['limit' => 20, 'null' => true])
              ->addColumn('role', 'enum', ['values' => ['admin', 'manager', 'teacher'], 'null' => false])
              ->addColumn('status', 'enum', ['values' => ['active', 'inactive'], 'default' => 'active'])
              ->addColumn('created', 'datetime', ['null' => false])
              ->addColumn('modified', 'datetime', ['null' => false])
              ->addIndex(['username'], ['unique' => true])
              ->addIndex(['email'], ['unique' => true])
              ->create();
    }
}
