<?php
declare(strict_types=1);

use Migrations\BaseMigration;

class CreateFinances extends BaseMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/migrations/4/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('finances');
        $table->addColumn('type', 'enum', ['values' => ['income', 'expense'], 'null' => false])
              ->addColumn('category', 'string', ['limit' => 100, 'null' => false])
              ->addColumn('description', 'text', ['null' => false])
              ->addColumn('amount', 'decimal', ['precision' => 15, 'scale' => 2, 'null' => false])
              ->addColumn('transaction_date', 'date', ['null' => false])
              ->addColumn('student_id', 'integer', ['null' => true])
              ->addColumn('created_by', 'integer', ['null' => false])
              ->addColumn('created', 'datetime', ['null' => false])
              ->addColumn('modified', 'datetime', ['null' => false])
              ->addIndex(['type'])
              ->addIndex(['category'])
              ->addIndex(['student_id'])
              ->addIndex(['created_by'])
              ->addIndex(['transaction_date'])
              ->addForeignKey('student_id', 'students', 'id', ['delete' => 'SET_NULL', 'update' => 'CASCADE'])
              ->addForeignKey('created_by', 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
              ->create();
    }
}
