<?php
declare(strict_types=1);

use Migrations\BaseMigration;

class CreateSchedules extends BaseMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/migrations/4/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('schedules');
        $table->addColumn('class_id', 'integer', ['null' => false])
              ->addColumn('time_slot_id', 'integer', ['null' => false])
              ->addColumn('teacher_id', 'integer', ['null' => false])
              ->addColumn('schedule_date', 'date', ['null' => false])
              ->addColumn('status', 'enum', ['values' => ['scheduled', 'completed', 'cancelled'], 'default' => 'scheduled'])
              ->addColumn('notes', 'text', ['null' => true])
              ->addColumn('created', 'datetime', ['null' => false])
              ->addColumn('modified', 'datetime', ['null' => false])
              ->addIndex(['class_id'])
              ->addIndex(['time_slot_id'])
              ->addIndex(['teacher_id'])
              ->addIndex(['schedule_date'])
              ->addIndex(['class_id', 'time_slot_id', 'schedule_date'], ['unique' => true])
              ->create();
    }
}
