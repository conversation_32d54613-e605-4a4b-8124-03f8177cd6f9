{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "651c87f3cab2896105c99205c02d5816", "packages": [{"name": "cakephp/authentication", "version": "3.2.5", "source": {"type": "git", "url": "https://github.com/cakephp/authentication.git", "reference": "445aef3af6350cad22f19530882cf5f758d69134"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/authentication/zipball/445aef3af6350cad22f19530882cf5f758d69134", "reference": "445aef3af6350cad22f19530882cf5f758d69134", "shasum": ""}, "require": {"cakephp/http": "^5.0", "laminas/laminas-diactoros": "^3.0", "php": ">=8.1", "psr/http-client": "^1.0", "psr/http-message": "^1.1 || ^2.0", "psr/http-server-handler": "^1.0", "psr/http-server-middleware": "^1.0"}, "require-dev": {"cakephp/cakephp": "^5.1.0", "cakephp/cakephp-codesniffer": "^5.0", "firebase/php-jwt": "^6.2", "phpunit/phpunit": "^10.5.5 || ^11.1.3"}, "suggest": {"cakephp/cakephp": "Install full core to use \"CookieAuthenticator\".", "cakephp/orm": "To use \"OrmResolver\" (Not needed separately if using full CakePHP framework).", "cakephp/utility": "Provides CakePHP security methods. Required for the JWT adapter and Legacy password hasher.", "ext-ldap": "Make sure this php extension is installed and enabled on your system if you want to use the built-in LDAP adapter for \"LdapIdentifier\".", "firebase/php-jwt": "If you want to use the JWT adapter add this dependency"}, "type": "cakephp-plugin", "autoload": {"psr-4": {"Authentication\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/authentication/graphs/contributors"}], "description": "Authentication plugin for CakePHP", "homepage": "https://cakephp.org", "keywords": ["Authentication", "auth", "cakephp", "middleware"], "support": {"docs": "https://book.cakephp.org/authentication/3/en/", "forum": "https://discourse.cakephp.org/", "issues": "https://github.com/cakephp/authentication/issues", "source": "https://github.com/cakephp/authentication"}, "time": "2025-05-14T14:03:55+00:00"}, {"name": "cakephp/authorization", "version": "3.4.1", "source": {"type": "git", "url": "https://github.com/cakephp/authorization.git", "reference": "ff151d694b592a683350f420dd9e58b7c0a762c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/authorization/zipball/ff151d694b592a683350f420dd9e58b7c0a762c4", "reference": "ff151d694b592a683350f420dd9e58b7c0a762c4", "shasum": ""}, "require": {"cakephp/http": "^5.1", "php": ">=8.1", "psr/http-client": "^1.0", "psr/http-message": "^1.1 || ^2.0", "psr/http-server-handler": "^1.0", "psr/http-server-middleware": "^1.0"}, "require-dev": {"cakephp/authentication": "^3.0", "cakephp/bake": "^3.2", "cakephp/cakephp": "^5.1", "cakephp/cakephp-codesniffer": "^5.1", "phpunit/phpunit": "^10.5.5 || ^11.1.3"}, "suggest": {"cakephp/http": "To use \"RequestPolicyInterface\" (Not needed separately if using full CakePHP framework).", "cakephp/orm": "To use \"OrmResolver\" (Not needed separately if using full CakePHP framework)."}, "type": "cakephp-plugin", "autoload": {"psr-4": {"Authorization\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/authorization/graphs/contributors"}], "description": "Authorization abstraction layer plugin for CakePHP", "keywords": ["access", "auth", "authorization", "cakephp"], "support": {"docs": "https://cakephp.org/authorization/2/en/", "forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/authorization/issues", "source": "https://github.com/cakephp/authorization"}, "time": "2025-01-23T11:43:11+00:00"}, {"name": "cakephp/cakephp", "version": "5.1.6", "source": {"type": "git", "url": "https://github.com/cakephp/cakephp.git", "reference": "4b8915cf32949cac5c798af2ff65f688c4d76d21"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/cakephp/zipball/4b8915cf32949cac5c798af2ff65f688c4d76d21", "reference": "4b8915cf32949cac5c798af2ff65f688c4d76d21", "shasum": ""}, "require": {"cakephp/chronos": "^3.1", "composer/ca-bundle": "^1.5", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "laminas/laminas-diactoros": "^3.3", "laminas/laminas-httphandlerrunner": "^2.6", "league/container": "^4.2", "php": ">=8.1", "psr/container": "^1.1 || ^2.0", "psr/http-client": "^1.0.2", "psr/http-factory": "^1.1", "psr/http-message": "^1.1 || ^2.0", "psr/http-server-handler": "^1.0.2", "psr/http-server-middleware": "^1.0.2", "psr/log": "^3.0", "psr/simple-cache": "^2.0 || ^3.0"}, "provide": {"psr/container-implementation": "^2.0", "psr/http-client-implementation": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/http-server-handler-implementation": "^1.0", "psr/http-server-middleware-implementation": "^1.0", "psr/log-implementation": "^3.0", "psr/simple-cache-implementation": "^3.0"}, "replace": {"cakephp/cache": "self.version", "cakephp/collection": "self.version", "cakephp/console": "self.version", "cakephp/core": "self.version", "cakephp/database": "self.version", "cakephp/datasource": "self.version", "cakephp/event": "self.version", "cakephp/form": "self.version", "cakephp/http": "self.version", "cakephp/i18n": "self.version", "cakephp/log": "self.version", "cakephp/orm": "self.version", "cakephp/utility": "self.version", "cakephp/validation": "self.version"}, "require-dev": {"cakephp/cakephp-codesniffer": "^5.0", "http-interop/http-factory-tests": "^2.0", "mikey179/vfsstream": "^1.6.10", "mockery/mockery": "^1.6", "paragonie/csp-builder": "^2.3 || ^3.0", "phpstan/extension-installer": "^1.3", "phpstan/phpstan": "1.12.7", "phpunit/phpunit": "^10.5.5 || ^11.1.3", "symplify/phpstan-rules": "^12.4"}, "suggest": {"ext-curl": "To enable more efficient network calls in Http\\Client.", "ext-openssl": "To use Security::encrypt() or have secure CSRF token generation.", "paragonie/csp-builder": "CSP builder, to use the CSP Middleware"}, "type": "library", "autoload": {"files": ["src/Core/functions.php", "src/Error/functions.php", "src/Collection/functions.php", "src/I18n/functions.php", "src/ORM/bootstrap.php", "src/Routing/functions.php", "src/Utility/bootstrap.php"], "psr-4": {"Cake\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/cakephp/graphs/contributors"}], "description": "The CakePHP framework", "homepage": "https://cakephp.org", "keywords": ["conventions over configuration", "dry", "form", "framework", "mvc", "orm", "psr-7", "rapid-development", "validation"], "support": {"forum": "https://discourse.cakephp.org/", "issues": "https://github.com/cakephp/cakephp/issues", "source": "https://github.com/cakephp/cakephp"}, "time": "2025-02-23T20:09:05+00:00"}, {"name": "cakephp/chronos", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/cakephp/chronos.git", "reference": "786d69e1ee4b735765cbdb5521b9603e9b98d650"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/chronos/zipball/786d69e1ee4b735765cbdb5521b9603e9b98d650", "reference": "786d69e1ee4b735765cbdb5521b9603e9b98d650", "shasum": ""}, "require": {"php": ">=8.1", "psr/clock": "^1.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"cakephp/cakephp-codesniffer": "^5.0", "phpunit/phpunit": "^10.1.0 || ^11.1.3"}, "type": "library", "autoload": {"psr-4": {"Cake\\Chronos\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}, {"name": "The CakePHP Team", "homepage": "https://cakephp.org"}], "description": "A simple API extension for DateTime.", "homepage": "https://cakephp.org", "keywords": ["date", "datetime", "time"], "support": {"issues": "https://github.com/cakephp/chronos/issues", "source": "https://github.com/cakephp/chronos"}, "time": "2024-07-18T03:18:04+00:00"}, {"name": "cakephp/migrations", "version": "4.6.4", "source": {"type": "git", "url": "https://github.com/cakephp/migrations.git", "reference": "a735a6f39e8452727b6281834529d40bc6efe741"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/migrations/zipball/a735a6f39e8452727b6281834529d40bc6efe741", "reference": "a735a6f39e8452727b6281834529d40bc6efe741", "shasum": ""}, "require": {"cakephp/cache": "^5.0", "cakephp/orm": "^5.0", "php": ">=8.1", "robmorgan/phinx": "^0.16.0"}, "require-dev": {"cakephp/bake": "^3.0", "cakephp/cakephp": "^5.0.11", "cakephp/cakephp-codesniffer": "^5.0", "phpunit/phpunit": "^10.5.5 || ^11.1.3"}, "suggest": {"cakephp/bake": "If you want to generate migrations.", "dereuromark/cakephp-ide-helper": "If you want to have IDE suggest/autocomplete when creating migrations."}, "type": "cakephp-plugin", "autoload": {"psr-4": {"Migrations\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/migrations/graphs/contributors"}], "description": "Database Migration plugin for CakePHP based on Phinx", "homepage": "https://github.com/cakephp/migrations", "keywords": ["cakephp", "cli", "migrations"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/migrations/issues", "source": "https://github.com/cakephp/migrations"}, "time": "2025-04-20T02:44:53+00:00"}, {"name": "cakephp/plugin-installer", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/cakephp/plugin-installer.git", "reference": "5420701fd47d82fe81805ebee34fbbcef34c52ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/plugin-installer/zipball/5420701fd47d82fe81805ebee34fbbcef34c52ba", "reference": "5420701fd47d82fe81805ebee34fbbcef34c52ba", "shasum": ""}, "require": {"composer-plugin-api": "^2.0", "php": ">=8.1"}, "require-dev": {"cakephp/cakephp-codesniffer": "^5.0", "composer/composer": "^2.0", "phpunit/phpunit": "^10.1.0"}, "type": "composer-plugin", "extra": {"class": "Cake\\Composer\\Plugin"}, "autoload": {"psr-4": {"Cake\\Composer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://cakephp.org"}], "description": "A composer installer for CakePHP 3.0+ plugins.", "support": {"issues": "https://github.com/cakephp/plugin-installer/issues", "source": "https://github.com/cakephp/plugin-installer/tree/2.0.1"}, "time": "2023-09-10T10:02:44+00:00"}, {"name": "composer/ca-bundle", "version": "1.5.7", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "d665d22c417056996c59019579f1967dfe5c1e82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/d665d22c417056996c59019579f1967dfe5c1e82", "reference": "d665d22c417056996c59019579f1967dfe5c1e82", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8 || ^9", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/process": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.5.7"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-05-26T15:08:54+00:00"}, {"name": "laminas/laminas-diactoros", "version": "3.6.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-diactoros.git", "reference": "b068eac123f21c0e592de41deeb7403b88e0a89f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-diactoros/zipball/b068eac123f21c0e592de41deeb7403b88e0a89f", "reference": "b068eac123f21c0e592de41deeb7403b88e0a89f", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/http-factory": "^1.1", "psr/http-message": "^1.1 || ^2.0"}, "conflict": {"amphp/amp": "<2.6.4"}, "provide": {"psr/http-factory-implementation": "^1.0", "psr/http-message-implementation": "^1.1 || ^2.0"}, "require-dev": {"ext-curl": "*", "ext-dom": "*", "ext-gd": "*", "ext-libxml": "*", "http-interop/http-factory-tests": "^2.2.0", "laminas/laminas-coding-standard": "~3.0.0", "php-http/psr7-integration-tests": "^1.4.0", "phpunit/phpunit": "^10.5.36", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "type": "library", "extra": {"laminas": {"module": "Laminas\\Diactoros", "config-provider": "Laminas\\Diactoros\\ConfigProvider"}}, "autoload": {"files": ["src/functions/create_uploaded_file.php", "src/functions/marshal_headers_from_sapi.php", "src/functions/marshal_method_from_sapi.php", "src/functions/marshal_protocol_version_from_sapi.php", "src/functions/normalize_server.php", "src/functions/normalize_uploaded_files.php", "src/functions/parse_cookie_header.php"], "psr-4": {"Laminas\\Diactoros\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "PSR HTTP Message implementations", "homepage": "https://laminas.dev", "keywords": ["http", "laminas", "psr", "psr-17", "psr-7"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-diactoros/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-diactoros/issues", "rss": "https://github.com/laminas/laminas-diactoros/releases.atom", "source": "https://github.com/laminas/laminas-diactoros"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-05-05T16:03:34+00:00"}, {"name": "laminas/laminas-httphandlerrunner", "version": "2.12.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-httphandlerrunner.git", "reference": "b14da3519c650e9436e410cfedee6f860312eff9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-httphandlerrunner/zipball/b14da3519c650e9436e410cfedee6f860312eff9", "reference": "b14da3519c650e9436e410cfedee6f860312eff9", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-message-implementation": "^1.0 || ^2.0", "psr/http-server-handler": "^1.0"}, "require-dev": {"laminas/laminas-coding-standard": "~3.1.0", "laminas/laminas-diactoros": "^3.6.0", "phpunit/phpunit": "^10.5.46", "psalm/plugin-phpunit": "^0.19.5", "vimeo/psalm": "^6.10.3"}, "type": "library", "extra": {"laminas": {"config-provider": "Laminas\\HttpHandlerRunner\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\HttpHandlerRunner\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Execute PSR-15 RequestHandlerInterface instances and emit responses they generate.", "homepage": "https://laminas.dev", "keywords": ["components", "laminas", "mezzio", "psr-15", "psr-7"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-httphandlerrunner/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-httphandlerrunner/issues", "rss": "https://github.com/laminas/laminas-httphandlerrunner/releases.atom", "source": "https://github.com/laminas/laminas-httphandlerrunner"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-05-13T21:21:16+00:00"}, {"name": "league/container", "version": "4.2.5", "source": {"type": "git", "url": "https://github.com/thephpleague/container.git", "reference": "d3cebb0ff4685ff61c749e54b27db49319e2ec00"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/container/zipball/d3cebb0ff4685ff61c749e54b27db49319e2ec00", "reference": "d3cebb0ff4685ff61c749e54b27db49319e2ec00", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "psr/container": "^1.1 || ^2.0"}, "provide": {"psr/container-implementation": "^1.0"}, "replace": {"orno/di": "~2.0"}, "require-dev": {"nette/php-generator": "^3.4", "nikic/php-parser": "^4.10", "phpstan/phpstan": "^0.12.47", "phpunit/phpunit": "^8.5.17", "roave/security-advisories": "dev-latest", "scrutinizer/ocular": "^1.8", "squizlabs/php_codesniffer": "^3.6"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev", "dev-2.x": "2.x-dev", "dev-3.x": "3.x-dev", "dev-4.x": "4.x-dev", "dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"League\\Container\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A fast and intuitive dependency injection container.", "homepage": "https://github.com/thephpleague/container", "keywords": ["container", "dependency", "di", "injection", "league", "provider", "service"], "support": {"issues": "https://github.com/thephpleague/container/issues", "source": "https://github.com/thephpleague/container/tree/4.2.5"}, "funding": [{"url": "https://github.com/philipobenito", "type": "github"}], "time": "2025-05-20T12:55:37+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "4.8.09", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "a06fe2e546a06bb8c2639d6823d5250b2efb3209"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/a06fe2e546a06bb8c2639d6823d5250b2efb3209", "reference": "a06fe2e546a06bb8c2639d6823d5250b2efb3209", "shasum": ""}, "require": {"php": ">=8.0", "psr/cache": "^3.0", "psr/simple-cache": "^3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^v3.65.0", "phpbench/phpbench": "^1.2", "phpstan/phpstan": "^1.12.x-dev", "phpunit/phpunit": "^9.6.18", "squizlabs/php_codesniffer": "^3.11.1"}, "type": "library", "autoload": {"psr-4": {"Detection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "support": {"issues": "https://github.com/serbanghita/Mobile-Detect/issues", "source": "https://github.com/serbanghita/Mobile-Detect/tree/4.8.09"}, "funding": [{"url": "https://github.com/serbanghita", "type": "github"}], "time": "2024-12-10T15:32:06+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/http-server-handler", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-handler.git", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-handler/zipball/84c4fb66179be4caaf8e97bd239203245302e7d4", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side request handler", "keywords": ["handler", "http", "http-interop", "psr", "psr-15", "psr-7", "request", "response", "server"], "support": {"source": "https://github.com/php-fig/http-server-handler/tree/1.0.2"}, "time": "2023-04-10T20:06:20+00:00"}, {"name": "psr/http-server-middleware", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-middleware.git", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-middleware/zipball/c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-handler": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side middleware", "keywords": ["http", "http-interop", "middleware", "psr", "psr-15", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-server-middleware/issues", "source": "https://github.com/php-fig/http-server-middleware/tree/1.0.2"}, "time": "2023-04-11T06:14:47+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "robmorgan/phinx", "version": "0.16.9", "source": {"type": "git", "url": "https://github.com/cakephp/phinx.git", "reference": "524ebdeb0e1838a845d752a3418726b38cd1e654"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/phinx/zipball/524ebdeb0e1838a845d752a3418726b38cd1e654", "reference": "524ebdeb0e1838a845d752a3418726b38cd1e654", "shasum": ""}, "require": {"cakephp/database": "^5.0.2", "composer-runtime-api": "^2.0", "php-64bit": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/config": "^4.0|^5.0|^6.0|^7.0", "symfony/console": "^6.0|^7.0"}, "require-dev": {"cakephp/cakephp-codesniffer": "^5.0", "cakephp/i18n": "^5.0", "ext-json": "*", "ext-pdo": "*", "phpunit/phpunit": "^9.5.19", "symfony/yaml": "^4.0|^5.0|^6.0|^7.0"}, "suggest": {"ext-json": "Install if using JSON configuration format", "ext-pdo": "PDO extension is needed", "symfony/yaml": "Install if using YAML configuration format"}, "bin": ["bin/phinx"], "type": "library", "autoload": {"psr-4": {"Phinx\\": "src/Phinx/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://robmorgan.id.au", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://shadowhand.me", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "CakePHP Community", "homepage": "https://github.com/cakephp/phinx/graphs/contributors", "role": "Developer"}], "description": "Phinx makes it ridiculously easy to manage the database migrations for your PHP app.", "homepage": "https://phinx.org", "keywords": ["database", "database migrations", "db", "migrations", "phinx"], "support": {"issues": "https://github.com/cakephp/phinx/issues", "source": "https://github.com/cakephp/phinx/tree/0.16.9"}, "time": "2025-05-25T16:07:44+00:00"}, {"name": "symfony/config", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "ba62ae565f1327c2f6366726312ed828c85853bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/ba62ae565f1327c2f6366726312ed828c85853bc", "reference": "ba62ae565f1327c2f6366726312ed828c85853bc", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/filesystem": "^7.1", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<6.4", "symfony/service-contracts": "<2.5"}, "require-dev": {"symfony/event-dispatcher": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-15T09:04:05+00:00"}, {"name": "symfony/console", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "66c1440edf6f339fd82ed6c7caa76cb006211b44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/66c1440edf6f339fd82ed6c7caa76cb006211b44", "reference": "66c1440edf6f339fd82ed6c7caa76cb006211b44", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^7.2"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-24T10:34:04+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/63afe740e99a13ba87ec199bb07bbdee937a5b62", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/filesystem", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/b8dce482de9d7c9fe2891155035a7248ab5c7fdb", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-25T15:15:23+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/service-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-25T09:37:31+00:00"}, {"name": "symfony/string", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "f3570b8c61ca887a9e2938e85cb6458515d2b125"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/f3570b8c61ca887a9e2938e85cb6458515d2b125", "reference": "f3570b8c61ca887a9e2938e85cb6458515d2b125", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-20T20:19:01+00:00"}], "packages-dev": [{"name": "brick/varexporter", "version": "0.6.0", "source": {"type": "git", "url": "https://github.com/brick/varexporter.git", "reference": "af98bfc2b702a312abbcaff37656dbe419cec5bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/varexporter/zipball/af98bfc2b702a312abbcaff37656dbe419cec5bc", "reference": "af98bfc2b702a312abbcaff37656dbe419cec5bc", "shasum": ""}, "require": {"nikic/php-parser": "^5.0", "php": "^8.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^10.5", "vimeo/psalm": "6.8.4"}, "type": "library", "autoload": {"psr-4": {"Brick\\VarExporter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A powerful alternative to var_export(), which can export closures and objects without __set_state()", "keywords": ["var_export"], "support": {"issues": "https://github.com/brick/varexporter/issues", "source": "https://github.com/brick/varexporter/tree/0.6.0"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2025-02-20T17:42:39+00:00"}, {"name": "cakephp/bake", "version": "3.3.1", "source": {"type": "git", "url": "https://github.com/cakephp/bake.git", "reference": "91d32beebfc63acc15308b0663acf0e288d2a9d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/bake/zipball/91d32beebfc63acc15308b0663acf0e288d2a9d5", "reference": "91d32beebfc63acc15308b0663acf0e288d2a9d5", "shasum": ""}, "require": {"brick/varexporter": "^0.6.0", "cakephp/cakephp": "^5.1", "cakephp/twig-view": "^2.0.0", "nikic/php-parser": "^5.0.0", "php": ">=8.1"}, "require-dev": {"cakephp/cakephp-codesniffer": "^5.0.0", "cakephp/debug_kit": "^5.0.0", "phpunit/phpunit": "^10.5.5 || ^11.1.3"}, "type": "cakephp-plugin", "autoload": {"psr-4": {"Bake\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/bake/graphs/contributors"}], "description": "Bake plugin for CakePHP", "homepage": "https://github.com/cakephp/bake", "keywords": ["bake", "cakephp", "cli", "dev"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "issues": "https://github.com/cakephp/bake/issues", "source": "https://github.com/cakephp/bake"}, "time": "2025-04-18T22:59:57+00:00"}, {"name": "cakephp/cakephp-codesniffer", "version": "5.2.2", "source": {"type": "git", "url": "https://github.com/cakephp/cakephp-codesniffer.git", "reference": "3c62979556e98d0330f8af8b103ad1ade0a7067e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/cakephp-codesniffer/zipball/3c62979556e98d0330f8af8b103ad1ade0a7067e", "reference": "3c62979556e98d0330f8af8b103ad1ade0a7067e", "shasum": ""}, "require": {"php": ">=8.1.0", "phpstan/phpdoc-parser": "^2.1.0", "slevomat/coding-standard": "^8.16", "squizlabs/php_codesniffer": "^3.9"}, "require-dev": {"phpunit/phpunit": "^9.3.4"}, "type": "phpcodesniffer-standard", "autoload": {"psr-4": {"CakePHP\\": "CakePHP/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/cakephp-codesniffer/graphs/contributors"}], "description": "CakePHP CodeSniffer Standards", "homepage": "https://cakephp.org", "keywords": ["codesniffer", "framework"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/cakephp-codesniffer/issues", "source": "https://github.com/cakephp/cakephp-codesniffer"}, "time": "2025-06-07T17:04:07+00:00"}, {"name": "cakephp/debug_kit", "version": "5.1.2", "source": {"type": "git", "url": "https://github.com/cakephp/debug_kit.git", "reference": "b83ec9e0c62480f7cc9639e01cb9310bb7f0816d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/debug_kit/zipball/b83ec9e0c62480f7cc9639e01cb9310bb7f0816d", "reference": "b83ec9e0c62480f7cc9639e01cb9310bb7f0816d", "shasum": ""}, "require": {"cakephp/cakephp": "^5.1", "composer/composer": "^2.0", "doctrine/sql-formatter": "^1.1.3", "php": ">=8.1"}, "require-dev": {"cakephp/authorization": "^3.0", "cakephp/cakephp-codesniffer": "^5.0", "phpunit/phpunit": "^10.5.5 || ^11.1.3"}, "suggest": {"ext-pdo_sqlite": "DebugKit needs to store panel data in a database. SQLite is simple and easy to use."}, "type": "cakephp-plugin", "autoload": {"psr-4": {"DebugKit\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://mark-story.com", "role": "Author"}, {"name": "CakePHP Community", "homepage": "https://github.com/cakephp/debug_kit/graphs/contributors"}], "description": "CakePHP Debug Kit", "homepage": "https://github.com/cakephp/debug_kit", "keywords": ["cakephp", "debug", "dev", "kit"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/debug_kit/issues", "source": "https://github.com/cakephp/debug_kit"}, "time": "2024-12-30T18:48:17+00:00"}, {"name": "cakephp/twig-view", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/cakephp/twig-view.git", "reference": "b11df8e8734ae556d98b143192377dbc6a6f5360"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/twig-view/zipball/b11df8e8734ae556d98b143192377dbc6a6f5360", "reference": "b11df8e8734ae556d98b143192377dbc6a6f5360", "shasum": ""}, "require": {"cakephp/cakephp": "^5.0.0", "jasny/twig-extensions": "^1.3", "twig/markdown-extra": "^3.0", "twig/twig": "^3.11.1"}, "conflict": {"wyrihaximus/twig-view": "*"}, "require-dev": {"cakephp/cakephp-codesniffer": "^5.0", "cakephp/debug_kit": "^5.0", "michelf/php-markdown": "^1.9", "mikey179/vfsstream": "^1.6.10", "phpunit/phpunit": "^10.5.5 || ^11.1.3"}, "type": "cakephp-plugin", "autoload": {"psr-4": {"Cake\\TwigView\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/cakephp/graphs/contributors"}], "description": "Twig powered View for CakePHP", "keywords": ["cakephp", "template", "twig", "view"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/twig-view/issues", "source": "https://github.com/cakephp/twig-view"}, "time": "2024-10-11T07:53:08+00:00"}, {"name": "composer/class-map-generator", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/composer/class-map-generator.git", "reference": "134b705ddb0025d397d8318a75825fe3c9d1da34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/class-map-generator/zipball/134b705ddb0025d397d8318a75825fe3c9d1da34", "reference": "134b705ddb0025d397d8318a75825fe3c9d1da34", "shasum": ""}, "require": {"composer/pcre": "^2.1 || ^3.1", "php": "^7.2 || ^8.0", "symfony/finder": "^4.4 || ^5.3 || ^6 || ^7"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-deprecation-rules": "^1 || ^2", "phpstan/phpstan-phpunit": "^1 || ^2", "phpstan/phpstan-strict-rules": "^1.1 || ^2", "phpunit/phpunit": "^8", "symfony/filesystem": "^5.4 || ^6"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\ClassMapGenerator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Utilities to scan PHP code and generate class maps.", "keywords": ["classmap"], "support": {"issues": "https://github.com/composer/class-map-generator/issues", "source": "https://github.com/composer/class-map-generator/tree/1.6.1"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-03-24T13:50:44+00:00"}, {"name": "composer/composer", "version": "2.8.9", "source": {"type": "git", "url": "https://github.com/composer/composer.git", "reference": "b4e6bff2db7ce756ddb77ecee958a0f41f42bd9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/composer/zipball/b4e6bff2db7ce756ddb77ecee958a0f41f42bd9d", "reference": "b4e6bff2db7ce756ddb77ecee958a0f41f42bd9d", "shasum": ""}, "require": {"composer/ca-bundle": "^1.5", "composer/class-map-generator": "^1.4.0", "composer/metadata-minifier": "^1.0", "composer/pcre": "^2.2 || ^3.2", "composer/semver": "^3.3", "composer/spdx-licenses": "^1.5.7", "composer/xdebug-handler": "^2.0.2 || ^3.0.3", "justinrainbow/json-schema": "^6.3.1", "php": "^7.2.5 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "react/promise": "^2.11 || ^3.2", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.2", "seld/signal-handler": "^2.0", "symfony/console": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/filesystem": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/finder": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/polyfill-php73": "^1.24", "symfony/polyfill-php80": "^1.24", "symfony/polyfill-php81": "^1.24", "symfony/process": "^5.4.35 || ^6.3.12 || ^7.0.3"}, "require-dev": {"phpstan/phpstan": "^1.11.8", "phpstan/phpstan-deprecation-rules": "^1.2.0", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-strict-rules": "^1.6.0", "phpstan/phpstan-symfony": "^1.4.0", "symfony/phpunit-bridge": "^6.4.3 || ^7.0.1"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "bin": ["bin/composer"], "type": "library", "extra": {"phpstan": {"includes": ["phpstan/rules.neon"]}, "branch-alias": {"dev-main": "2.8-dev"}}, "autoload": {"psr-4": {"Composer\\": "src/Composer/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Composer helps you declare, manage and install dependencies of PHP projects. It ensures you have the right stack everywhere.", "homepage": "https://getcomposer.org/", "keywords": ["autoload", "dependency", "package"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/composer/issues", "security": "https://github.com/composer/composer/security/policy", "source": "https://github.com/composer/composer/tree/2.8.9"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-05-13T12:01:37+00:00"}, {"name": "composer/metadata-minifier", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/composer/metadata-minifier.git", "reference": "c549d23829536f0d0e984aaabbf02af91f443207"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/metadata-minifier/zipball/c549d23829536f0d0e984aaabbf02af91f443207", "reference": "c549d23829536f0d0e984aaabbf02af91f443207", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"composer/composer": "^2", "phpstan/phpstan": "^0.12.55", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\MetadataMinifier\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Small utility library that handles metadata minification and expansion.", "keywords": ["composer", "compression"], "support": {"issues": "https://github.com/composer/metadata-minifier/issues", "source": "https://github.com/composer/metadata-minifier/tree/1.0.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-04-07T13:37:33+00:00"}, {"name": "composer/pcre", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-strict-rules": "^1 || ^2", "phpunit/phpunit": "^8 || ^9"}, "type": "library", "extra": {"phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-11-12T16:29:46+00:00"}, {"name": "composer/semver", "version": "3.4.3", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-09-19T14:15:21+00:00"}, {"name": "composer/spdx-licenses", "version": "1.5.9", "source": {"type": "git", "url": "https://github.com/composer/spdx-licenses.git", "reference": "edf364cefe8c43501e21e88110aac10b284c3c9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/spdx-licenses/zipball/edf364cefe8c43501e21e88110aac10b284c3c9f", "reference": "edf364cefe8c43501e21e88110aac10b284c3c9f", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/spdx-licenses/issues", "source": "https://github.com/composer/spdx-licenses/tree/1.5.9"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-05-12T21:07:07+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.5", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/6c1925561632e83d60a44492e0b344cf48ab85ef", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5 || ^9.6 || ^10.5"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-05-06T16:37:16+00:00"}, {"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/composer-installer.git", "reference": "18a95476797ed480b3f2598984bc6f7e1eecc9a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/composer-installer/zipball/18a95476797ed480b3f2598984bc6f7e1eecc9a8", "reference": "18a95476797ed480b3f2598984bc6f7e1eecc9a8", "shasum": ""}, "require": {"composer-plugin-api": "^2.2", "php": ">=5.4", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "^2.2", "ext-json": "*", "ext-zip": "*", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0", "yoast/phpunit-polyfills": "^1.0"}, "type": "composer-plugin", "extra": {"class": "PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "autoload": {"psr-4": {"PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://frenck.dev", "role": "Open source developer"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/PHPCSStandards/composer-installer/issues", "security": "https://github.com/PHPCSStandards/composer-installer/security/policy", "source": "https://github.com/PHPCSStandards/composer-installer"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "time": "2025-05-28T16:05:48+00:00"}, {"name": "doctrine/sql-formatter", "version": "1.5.2", "source": {"type": "git", "url": "https://github.com/doctrine/sql-formatter.git", "reference": "d6d00aba6fd2957fe5216fe2b7673e9985db20c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/sql-formatter/zipball/d6d00aba6fd2957fe5216fe2b7673e9985db20c8", "reference": "d6d00aba6fd2957fe5216fe2b7673e9985db20c8", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "ergebnis/phpunit-slow-test-detector": "^2.14", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5"}, "bin": ["bin/sql-formatter"], "type": "library", "autoload": {"psr-4": {"Doctrine\\SqlFormatter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/doctrine/sql-formatter/", "keywords": ["highlight", "sql"], "support": {"issues": "https://github.com/doctrine/sql-formatter/issues", "source": "https://github.com/doctrine/sql-formatter/tree/1.5.2"}, "time": "2025-01-24T11:45:48+00:00"}, {"name": "jasny/twig-extensions", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/jasny/twig-extensions.git", "reference": "8a5ca5f49317bf421a519556ad2e876820d41e01"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jasny/twig-extensions/zipball/8a5ca5f49317bf421a519556ad2e876820d41e01", "reference": "8a5ca5f49317bf421a519556ad2e876820d41e01", "shasum": ""}, "require": {"php": ">=7.4.0", "twig/twig": "^2.7 | ^3.0"}, "require-dev": {"ext-intl": "*", "ext-json": "*", "ext-pcre": "*", "phpstan/phpstan": "^1.12.0", "phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.10"}, "suggest": {"ext-intl": "Required for the use of the LocalDate Twig extension", "ext-pcre": "Required for the use of the PCRE Twig extension"}, "type": "library", "autoload": {"psr-4": {"Jasny\\Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "a<PERSON><PERSON>@jasny.net", "homepage": "http://www.jasny.net"}], "description": "A set of useful Twig filters", "homepage": "http://github.com/jasny/twig-extensions#README", "keywords": ["PCRE", "array", "date", "datetime", "preg", "regex", "templating", "text", "time"], "support": {"issues": "https://github.com/jasny/twig-extensions/issues", "source": "https://github.com/jasny/twig-extensions"}, "time": "2024-09-03T09:04:53+00:00"}, {"name": "jose<PERSON><PERSON><PERSON>/dotenv", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/josegonzalez/php-dotenv.git", "reference": "e97dbd3db53508dcd536e73ec787a7f11458d41d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/josegonzalez/php-dotenv/zipball/e97dbd3db53508dcd536e73ec787a7f11458d41d", "reference": "e97dbd3db53508dcd536e73ec787a7f11458d41d", "shasum": ""}, "require": {"m1/env": "2.*", "php": ">=5.5.0"}, "require-dev": {"php-coveralls/php-coveralls": "~2.0", "php-mock/php-mock-phpunit": "~1.1||~2.0", "squizlabs/php_codesniffer": "~2.9||~3.7"}, "type": "library", "autoload": {"psr-0": {"josegonzalez\\Dotenv": ["src", "tests"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://josediazgonzalez.com", "role": "Maintainer"}], "description": "dotenv file parsing for PHP", "homepage": "https://github.com/josegonzalez/php-dotenv", "keywords": ["configuration", "dotenv", "php"], "support": {"issues": "https://github.com/josegonzalez/php-dotenv/issues", "source": "https://github.com/josegonzalez/php-dotenv/tree/4.0.0"}, "time": "2023-05-29T22:49:26+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "6.4.2", "source": {"type": "git", "url": "https://github.com/jsonrainbow/json-schema.git", "reference": "ce1fd2d47799bb60668643bc6220f6278a4c1d02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jsonrainbow/json-schema/zipball/ce1fd2d47799bb60668643bc6220f6278a4c1d02", "reference": "ce1fd2d47799bb60668643bc6220f6278a4c1d02", "shasum": ""}, "require": {"ext-json": "*", "marc-mabe/php-enum": "^4.0", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.3.0", "json-schema/json-schema-test-suite": "1.2.0", "marc-mabe/php-enum-phpstan": "^2.0", "phpspec/prophecy": "^1.19", "phpstan/phpstan": "^1.12", "phpunit/phpunit": "^8.5"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "6.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/jsonrainbow/json-schema", "keywords": ["json", "schema"], "support": {"issues": "https://github.com/jsonrainbow/json-schema/issues", "source": "https://github.com/jsonrainbow/json-schema/tree/6.4.2"}, "time": "2025-06-03T18:27:04+00:00"}, {"name": "m1/env", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/m1/Env.git", "reference": "5c296e3e13450a207e12b343f3af1d7ab569f6f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/m1/Env/zipball/5c296e3e13450a207e12b343f3af1d7ab569f6f3", "reference": "5c296e3e13450a207e12b343f3af1d7ab569f6f3", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "4.*", "scrutinizer/ocular": "~1.1", "squizlabs/php_codesniffer": "^2.3"}, "suggest": {"josegonzalez/dotenv": "For loading of .env", "m1/vars": "For loading of configs"}, "type": "library", "autoload": {"psr-4": {"M1\\Env\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://milescroxford.com", "role": "Developer"}], "description": "Env is a lightweight library bringing .env file parser compatibility to PHP. In short - it enables you to read .env files with PHP.", "homepage": "https://github.com/m1/Env", "keywords": [".env", "config", "dotenv", "env", "loader", "m1", "parser", "support"], "support": {"issues": "https://github.com/m1/Env/issues", "source": "https://github.com/m1/Env/tree/2.2.0"}, "time": "2020-02-19T09:02:13+00:00"}, {"name": "marc-mabe/php-enum", "version": "v4.7.1", "source": {"type": "git", "url": "https://github.com/marc-mabe/php-enum.git", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/marc-mabe/php-enum/zipball/7159809e5cfa041dca28e61f7f7ae58063aae8ed", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed", "shasum": ""}, "require": {"ext-reflection": "*", "php": "^7.1 | ^8.0"}, "require-dev": {"phpbench/phpbench": "^0.16.10 || ^1.0.4", "phpstan/phpstan": "^1.3.1", "phpunit/phpunit": "^7.5.20 | ^8.5.22 | ^9.5.11", "vimeo/psalm": "^4.17.0 | ^5.26.1"}, "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.2-dev", "dev-master": "4.7-dev"}}, "autoload": {"psr-4": {"MabeEnum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://mabe.berlin/", "role": "Lead"}], "description": "Simple and fast implementation of enumerations with native PHP", "homepage": "https://github.com/marc-mabe/php-enum", "keywords": ["enum", "enum-map", "enum-set", "enumeration", "enumerator", "enummap", "enumset", "map", "set", "type", "type-hint", "<PERSON><PERSON>t"], "support": {"issues": "https://github.com/marc-mabe/php-enum/issues", "source": "https://github.com/marc-mabe/php-enum/tree/v4.7.1"}, "time": "2024-11-28T04:54:44+00:00"}, {"name": "myclabs/deep-copy", "version": "1.13.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "1720ddd719e16cf0db4eb1c6eca108031636d46c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/1720ddd719e16cf0db4eb1c6eca108031636d46c", "reference": "1720ddd719e16cf0db4eb1c6eca108031636d46c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2025-04-29T12:36:36+00:00"}, {"name": "nikic/php-parser", "version": "v5.5.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "ae59794362fe85e051a58ad36b289443f57be7a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/ae59794362fe85e051a58ad36b289443f57be7a9", "reference": "ae59794362fe85e051a58ad36b289443f57be7a9", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.5.0"}, "time": "2025-05-31T08:24:38+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^5.3.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.6", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/2.1.0"}, "time": "2025-02-19T13:28:12+00:00"}, {"name": "phpunit/php-code-coverage", "version": "11.0.10", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "1a800a7446add2d79cc6b3c01c45381810367d76"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/1a800a7446add2d79cc6b3c01c45381810367d76", "reference": "1a800a7446add2d79cc6b3c01c45381810367d76", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^5.4.0", "php": ">=8.2", "phpunit/php-file-iterator": "^5.1.0", "phpunit/php-text-template": "^4.0.1", "sebastian/code-unit-reverse-lookup": "^4.0.1", "sebastian/complexity": "^4.0.1", "sebastian/environment": "^7.2.0", "sebastian/lines-of-code": "^3.0.1", "sebastian/version": "^5.0.2", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^11.5.2"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "11.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/show"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/php-code-coverage", "type": "tidelift"}], "time": "2025-06-18T08:56:18+00:00"}, {"name": "phpunit/php-file-iterator", "version": "5.1.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "118cfaaa8bc5aef3287bf315b6060b1174754af6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-file-iterator/zipball/118cfaaa8bc5aef3287bf315b6060b1174754af6", "reference": "118cfaaa8bc5aef3287bf315b6060b1174754af6", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/5.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-08-27T05:02:59+00:00"}, {"name": "phpunit/php-invoker", "version": "5.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "c1ca3814734c07492b3d4c5f794f4b0995333da2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/c1ca3814734c07492b3d4c5f794f4b0995333da2", "reference": "c1ca3814734c07492b3d4c5f794f4b0995333da2", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^11.0"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/5.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T05:07:44+00:00"}, {"name": "phpunit/php-text-template", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "3e0404dc6b300e6bf56415467ebcb3fe4f33e964"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/3e0404dc6b300e6bf56415467ebcb3fe4f33e964", "reference": "3e0404dc6b300e6bf56415467ebcb3fe4f33e964", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/4.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T05:08:43+00:00"}, {"name": "phpunit/php-timer", "version": "7.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "3b415def83fbcb41f991d9ebf16ae4ad8b7837b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/3b415def83fbcb41f991d9ebf16ae4ad8b7837b3", "reference": "3b415def83fbcb41f991d9ebf16ae4ad8b7837b3", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/security/policy", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/7.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T05:09:35+00:00"}, {"name": "phpunit/phpunit", "version": "11.5.24", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "6b07ab1047155cf38f82dd691787a277782271dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/6b07ab1047155cf38f82dd691787a277782271dd", "reference": "6b07ab1047155cf38f82dd691787a277782271dd", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.13.1", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=8.2", "phpunit/php-code-coverage": "^11.0.10", "phpunit/php-file-iterator": "^5.1.0", "phpunit/php-invoker": "^5.0.1", "phpunit/php-text-template": "^4.0.1", "phpunit/php-timer": "^7.0.1", "sebastian/cli-parser": "^3.0.2", "sebastian/code-unit": "^3.0.3", "sebastian/comparator": "^6.3.1", "sebastian/diff": "^6.0.2", "sebastian/environment": "^7.2.1", "sebastian/exporter": "^6.3.0", "sebastian/global-state": "^7.0.2", "sebastian/object-enumerator": "^6.0.1", "sebastian/type": "^5.1.2", "sebastian/version": "^5.0.2", "staabm/side-effects-detector": "^1.0.5"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-main": "11.5-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/11.5.24"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2025-06-20T11:31:02+00:00"}, {"name": "react/promise", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "8a164643313c71354582dc850b42b33fa12a4b63"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/8a164643313c71354582dc850b42b33fa12a4b63", "reference": "8a164643313c71354582dc850b42b33fa12a4b63", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpstan/phpstan": "1.10.39 || 1.4.10", "phpunit/phpunit": "^9.6 || ^7.5"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v3.2.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-05-24T10:39:05+00:00"}, {"name": "sebastian/cli-parser", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "15c5dd40dc4f38794d383bb95465193f5e0ae180"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/15c5dd40dc4f38794d383bb95465193f5e0ae180", "reference": "15c5dd40dc4f38794d383bb95465193f5e0ae180", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T04:41:36+00:00"}, {"name": "sebastian/code-unit", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "54391c61e4af8078e5b276ab082b6d3c54c9ad64"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/54391c61e4af8078e5b276ab082b6d3c54c9ad64", "reference": "54391c61e4af8078e5b276ab082b6d3c54c9ad64", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-03-19T07:56:08+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "****************************************"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/****************************************", "reference": "****************************************", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/4.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T04:45:54+00:00"}, {"name": "sebastian/comparator", "version": "6.3.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "24b8fbc2c8e201bb1308e7b05148d6ab393b6959"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/24b8fbc2c8e201bb1308e7b05148d6ab393b6959", "reference": "24b8fbc2c8e201bb1308e7b05148d6ab393b6959", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "php": ">=8.2", "sebastian/diff": "^6.0", "sebastian/exporter": "^6.0"}, "require-dev": {"phpunit/phpunit": "^11.4"}, "suggest": {"ext-bcmath": "For comparing BcMath\\Number objects"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/6.3.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-03-07T06:57:01+00:00"}, {"name": "sebastian/complexity", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "ee41d384ab1906c68852636b6de493846e13e5a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/ee41d384ab1906c68852636b6de493846e13e5a0", "reference": "ee41d384ab1906c68852636b6de493846e13e5a0", "shasum": ""}, "require": {"nikic/php-parser": "^5.0", "php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "security": "https://github.com/sebastian<PERSON>mann/complexity/security/policy", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/4.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T04:49:50+00:00"}, {"name": "sebastian/diff", "version": "6.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "b4ccd857127db5d41a5b676f24b51371d76d8544"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/diff/zipball/b4ccd857127db5d41a5b676f24b51371d76d8544", "reference": "b4ccd857127db5d41a5b676f24b51371d76d8544", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/6.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T04:53:05+00:00"}, {"name": "sebastian/environment", "version": "7.2.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "a5c75038693ad2e8d4b6c15ba2403532647830c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/a5c75038693ad2e8d4b6c15ba2403532647830c4", "reference": "a5c75038693ad2e8d4b6c15ba2403532647830c4", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.3"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "7.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "https://github.com/sebastian<PERSON>mann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/environment/security/policy", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/7.2.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/environment", "type": "tidelift"}], "time": "2025-05-21T11:55:47+00:00"}, {"name": "sebastian/exporter", "version": "6.3.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "3473f61172093b2da7de1fb5782e1f24cc036dc3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/3473f61172093b2da7de1fb5782e1f24cc036dc3", "reference": "3473f61172093b2da7de1fb5782e1f24cc036dc3", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.2", "sebastian/recursion-context": "^6.0"}, "require-dev": {"phpunit/phpunit": "^11.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "security": "https://github.com/sebastian<PERSON>mann/exporter/security/policy", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/6.3.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-12-05T09:17:50+00:00"}, {"name": "sebastian/global-state", "version": "7.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "3be331570a721f9a4b5917f4209773de17f747d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/3be331570a721f9a4b5917f4209773de17f747d7", "reference": "3be331570a721f9a4b5917f4209773de17f747d7", "shasum": ""}, "require": {"php": ">=8.2", "sebastian/object-reflector": "^4.0", "sebastian/recursion-context": "^6.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "https://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/7.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T04:57:36+00:00"}, {"name": "sebastian/lines-of-code", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "d36ad0d782e5756913e42ad87cb2890f4ffe467a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/d36ad0d782e5756913e42ad87cb2890f4ffe467a", "reference": "d36ad0d782e5756913e42ad87cb2890f4ffe467a", "shasum": ""}, "require": {"nikic/php-parser": "^5.0", "php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/3.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T04:58:38+00:00"}, {"name": "sebastian/object-enumerator", "version": "6.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "f5b498e631a74204185071eb41f33f38d64608aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/f5b498e631a74204185071eb41f33f38d64608aa", "reference": "f5b498e631a74204185071eb41f33f38d64608aa", "shasum": ""}, "require": {"php": ">=8.2", "sebastian/object-reflector": "^4.0", "sebastian/recursion-context": "^6.0"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/6.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T05:00:13+00:00"}, {"name": "sebastian/object-reflector", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "6e1a43b411b2ad34146dee7524cb13a068bb35f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/6e1a43b411b2ad34146dee7524cb13a068bb35f9", "reference": "6e1a43b411b2ad34146dee7524cb13a068bb35f9", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/4.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T05:01:32+00:00"}, {"name": "sebastian/recursion-context", "version": "6.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "694d156164372abbd149a4b85ccda2e4670c0e16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/694d156164372abbd149a4b85ccda2e4670c0e16", "reference": "694d156164372abbd149a4b85ccda2e4670c0e16", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/6.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T05:10:34+00:00"}, {"name": "sebastian/type", "version": "5.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "a8a7e30534b0eb0c77cd9d07e82de1a114389f5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/type/zipball/a8a7e30534b0eb0c77cd9d07e82de1a114389f5e", "reference": "a8a7e30534b0eb0c77cd9d07e82de1a114389f5e", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/type/security/policy", "source": "https://github.com/sebastian<PERSON>mann/type/tree/5.1.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-03-18T13:35:50+00:00"}, {"name": "sebastian/version", "version": "5.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c687e3387b99f5b03b6caa64c74b63e2936ff874"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c687e3387b99f5b03b6caa64c74b63e2936ff874", "reference": "c687e3387b99f5b03b6caa64c74b63e2936ff874", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/version/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/5.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-10-09T05:16:32+00:00"}, {"name": "seld/jsonlint", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/Seldaek/jsonlint.git", "reference": "1748aaf847fc731cfad7725aec413ee46f0cc3a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/1748aaf847fc731cfad7725aec413ee46f0cc3a2", "reference": "1748aaf847fc731cfad7725aec413ee46f0cc3a2", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^8.5.13"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "support": {"issues": "https://github.com/Seldaek/jsonlint/issues", "source": "https://github.com/Seldaek/jsonlint/tree/1.11.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/seld/jsonlint", "type": "tidelift"}], "time": "2024-07-11T14:55:45+00:00"}, {"name": "seld/phar-utils", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/Seldaek/phar-utils.git", "reference": "ea2f4014f163c1be4c601b9b7bd6af81ba8d701c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/phar-utils/zipball/ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "reference": "ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\PharUtils\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "PHAR file format utilities, for when PHP phars you up", "keywords": ["phar"], "support": {"issues": "https://github.com/Seldaek/phar-utils/issues", "source": "https://github.com/Seldaek/phar-utils/tree/1.2.1"}, "time": "2022-08-31T10:31:18+00:00"}, {"name": "seld/signal-handler", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/Seldaek/signal-handler.git", "reference": "04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/signal-handler/zipball/04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98", "reference": "04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98", "shasum": ""}, "require": {"php": ">=7.2.0"}, "require-dev": {"phpstan/phpstan": "^1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^7.5.20 || ^8.5.23", "psr/log": "^1 || ^2 || ^3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Seld\\Signal\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Simple unix signal handler that silently fails where signals are not supported for easy cross-platform development", "keywords": ["posix", "sigint", "signal", "sigterm", "unix"], "support": {"issues": "https://github.com/Seldaek/signal-handler/issues", "source": "https://github.com/Seldaek/signal-handler/tree/2.0.2"}, "time": "2023-09-03T09:24:00+00:00"}, {"name": "slevomat/coding-standard", "version": "8.19.1", "source": {"type": "git", "url": "https://github.com/slevomat/coding-standard.git", "reference": "458d665acd49009efebd7e0cb385d71ae9ac3220"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/slevomat/coding-standard/zipball/458d665acd49009efebd7e0cb385d71ae9ac3220", "reference": "458d665acd49009efebd7e0cb385d71ae9ac3220", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.6.2 || ^0.7 || ^1.0", "php": "^7.4 || ^8.0", "phpstan/phpdoc-parser": "^2.1.0", "squizlabs/php_codesniffer": "^3.13.0"}, "require-dev": {"phing/phing": "3.0.1", "php-parallel-lint/php-parallel-lint": "1.4.0", "phpstan/phpstan": "2.1.17", "phpstan/phpstan-deprecation-rules": "2.0.3", "phpstan/phpstan-phpunit": "2.0.6", "phpstan/phpstan-strict-rules": "2.0.4", "phpunit/phpunit": "9.6.8|10.5.45|11.4.4|11.5.21|12.1.3"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"SlevomatCodingStandard\\": "SlevomatCodingStandard/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Slevomat Coding Standard for PHP_CodeSniffer complements Consistence Coding Standard by providing sniffs with additional checks.", "keywords": ["dev", "phpcs"], "support": {"issues": "https://github.com/slevomat/coding-standard/issues", "source": "https://github.com/slevomat/coding-standard/tree/8.19.1"}, "funding": [{"url": "https://github.com/kukulich", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/slevomat/coding-standard", "type": "tidelift"}], "time": "2025-06-09T17:53:57+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.13.2", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "reference": "5b5e3821314f947dd040c70f7992a64eac89025c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/5b5e3821314f947dd040c70f7992a64eac89025c", "reference": "5b5e3821314f947dd040c70f7992a64eac89025c", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "bin": ["bin/phpcbf", "bin/phpcs"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "Former lead"}, {"name": "<PERSON>", "role": "Current lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer/graphs/contributors"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHP_CodeSniffer/issues", "security": "https://github.com/PHPCSStandards/PHP_CodeSniffer/security/policy", "source": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "wiki": "https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "time": "2025-06-17T22:17:01+00:00"}, {"name": "staabm/side-effects-detector", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/staabm/side-effects-detector.git", "reference": "d8334211a140ce329c13726d4a715adbddd0a163"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/staabm/side-effects-detector/zipball/d8334211a140ce329c13726d4a715adbddd0a163", "reference": "d8334211a140ce329c13726d4a715adbddd0a163", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^1.12.6", "phpunit/phpunit": "^9.6.21", "symfony/var-dumper": "^5.4.43", "tomasvotruba/type-coverage": "1.0.0", "tomasvotruba/unused-public": "1.0.0"}, "type": "library", "autoload": {"classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A static analysis tool to detect side effects in PHP code", "keywords": ["static analysis"], "support": {"issues": "https://github.com/staabm/side-effects-detector/issues", "source": "https://github.com/staabm/side-effects-detector/tree/1.0.5"}, "funding": [{"url": "https://github.com/staabm", "type": "github"}], "time": "2024-10-20T05:08:20+00:00"}, {"name": "symfony/finder", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "ec2344cf77a48253bbca6939aa3d2477773ea63d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/ec2344cf77a48253bbca6939aa3d2477773ea63d", "reference": "ec2344cf77a48253bbca6939aa3d2477773ea63d", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-30T19:00:26+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-02T08:10:11+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/process", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "40c295f2deb408d5e9d2d32b8ba1dd61e36f05af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/40c295f2deb408d5e9d2d32b8ba1dd61e36f05af", "reference": "40c295f2deb408d5e9d2d32b8ba1dd61e36f05af", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-17T09:11:12+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}, {"name": "twig/markdown-extra", "version": "v3.21.0", "source": {"type": "git", "url": "https://github.com/twigphp/markdown-extra.git", "reference": "f4616e1dd375209dacf6026f846e6b537d036ce4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/markdown-extra/zipball/f4616e1dd375209dacf6026f846e6b537d036ce4", "reference": "f4616e1dd375209dacf6026f846e6b537d036ce4", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/deprecation-contracts": "^2.5|^3", "twig/twig": "^3.13|^4.0"}, "require-dev": {"erusev/parsedown": "dev-master as 1.x-dev", "league/commonmark": "^1.0|^2.0", "league/html-to-markdown": "^4.8|^5.0", "michelf/php-markdown": "^1.8|^2.0", "symfony/phpunit-bridge": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Twig\\Extra\\Markdown\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}], "description": "A Twig extension for Markdown", "homepage": "https://twig.symfony.com", "keywords": ["html", "markdown", "twig"], "support": {"source": "https://github.com/twigphp/markdown-extra/tree/v3.21.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-01-31T20:45:36+00:00"}, {"name": "twig/twig", "version": "v3.21.1", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "285123877d4dd97dd7c11842ac5fb7e86e60d81d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/285123877d4dd97dd7c11842ac5fb7e86e60d81d", "reference": "285123877d4dd97dd7c11842ac5fb7e86e60d81d", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"phpstan/phpstan": "^2.0", "psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.4|^7.0"}, "type": "library", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.21.1"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-05-03T07:21:55+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=8.1"}, "platform-dev": [], "plugin-api-version": "2.6.0"}